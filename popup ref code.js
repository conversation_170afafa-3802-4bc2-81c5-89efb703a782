!(function () {
    const u = {
            products: {
                "Standard T-shirt": {
                    colors: [
                        { name: "Asphalt", class: "checkbox-asphalt", category: "Dark Colors" },
                        { name: "Baby Blue", class: "checkbox-baby_blue", category: "Light Colors" },
                        { name: "<PERSON>", class: "checkbox-black", category: "Dark Colors" },
                        { name: "<PERSON>", class: "checkbox-brown", category: "Dark Colors" },
                        { name: "Cranberry", class: "checkbox-cranberry", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "<PERSON>", class: "checkbox-grass", category: "Dark Colors" },
                        { name: "<PERSON> Blue", class: "checkbox-heather_blue", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Kelly Green", class: "checkbox-kelly_green", category: "Dark Colors" },
                        { name: "<PERSON>", class: "checkbox-lemon", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "<PERSON>", class: "checkbox-olive", category: "Dark Colors" },
                        { name: "Orange", class: "checkbox-orange", category: "Neutral Colors" },
                        { name: "Pink", class: "checkbox-pink", category: "Light Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                        { name: "Silver", class: "checkbox-silver", category: "Light Colors" },
                        { name: "Slate", class: "checkbox-slate", category: "Neutral Colors" },
                        { name: "White", class: "checkbox-white", category: "Light Colors" },
                        { name: "Dark Green", class: "checkbox-dark_green", category: "Dark Colors" },
                        { name: "Burgundy", class: "checkbox-burgundy", category: "Dark Colors" },
                        { name: "Golden Yellow", class: "checkbox-golden_yellow", category: "Light Colors" },
                        { name: "Purple Heather", class: "checkbox-purple_heather", category: "Dark Colors" },
                        { name: "Red Heather", class: "checkbox-red_heather", category: "Dark Colors" },
                        { name: "Olive Heather", class: "checkbox-olive_heather", category: "Dark Colors" },
                        { name: "Pink Heather", class: "checkbox-pink_heather", category: "Light Colors" },
                        { name: "Sapphire", class: "checkbox-sapphire", category: "Dark Colors" },
                    ],
                },
                "Premium T-shirt": {
                    colors: [
                        { name: "Asphalt", class: "checkbox-asphalt", category: "Dark Colors" },
                        { name: "Baby Blue", class: "checkbox-baby_blue", category: "Light Colors" },
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Cranberry", class: "checkbox-cranberry", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Grass", class: "checkbox-grass", category: "Dark Colors" },
                        { name: "Heather Blue", class: "checkbox-heather_blue", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Kelly Green", class: "checkbox-kelly_green", category: "Dark Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Olive", class: "checkbox-olive", category: "Dark Colors" },
                        { name: "Pink", class: "checkbox-pink", category: "Light Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Slate", class: "checkbox-slate", category: "Neutral Colors" },
                        { name: "White", class: "checkbox-white", category: "Light Colors" },
                    ],
                },
                "V-neck T-shirt": {
                    colors: [
                        { name: "Baby Blue", class: "checkbox-baby_blue", category: "Light Colors" },
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Forest Green", class: "checkbox-forest_green", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Pink", class: "checkbox-pink", category: "Light Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Sapphire", class: "checkbox-sapphire", category: "Dark Colors" },
                    ],
                },
                "Tank Top": {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Neon Pink", class: "checkbox-neon_pink", category: "Light Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                        { name: "Sapphire", class: "checkbox-sapphire", category: "Dark Colors" },
                        { name: "White", class: "checkbox-white", category: "Light Colors" },
                    ],
                },
                "Long Sleeve T-shirt": {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                    ],
                },
                Raglan: {
                    colors: [
                        { name: "Black Athletic Heather", class: "checkbox-black_athletic_heather", category: "Neutral Colors" },
                        { name: "Black White", class: "checkbox-black_white", category: "Light Colors" },
                        { name: "Dark Heather White", class: "checkbox-dark_heather_white", category: "Light Colors" },
                        { name: "Navy Athletic Heather", class: "checkbox-navy_athletic_heather", category: "Neutral Colors" },
                        { name: "Navy White", class: "checkbox-navy_white", category: "Light Colors" },
                        { name: "Red White", class: "checkbox-red_white", category: "Light Colors" },
                        { name: "Royal Blue White", class: "checkbox-royal_blue_white", category: "Light Colors" },
                    ],
                },
                Sweatshirt: {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                    ],
                },
                "Pullover Hoodie": {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                        { name: "Asphalt", class: "checkbox-asphalt", category: "Dark Colors" },
                        { name: "Burgundy", class: "checkbox-burgundy", category: "Dark Colors" },
                        { name: "Sage Green", class: "checkbox-sage_green", category: "Dark Colors" },
                        { name: "Kelly Green", class: "checkbox-kelly_green", category: "Dark Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Bright Pink", class: "checkbox-bright_pink", category: "Light Colors" },
                        { name: "Dusty Blue", class: "checkbox-dusty_blue", category: "Light Colors" },
                        { name: "Dark Green", class: "checkbox-dark_green", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Lemon", class: "checkbox-lemon", category: "Light Colors" },
                    ],
                },
                "Zip Hoodie": {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                        { name: "Forest Green", class: "checkbox-forest_green", category: "Dark Colors" },
                        { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                        { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                        { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                        { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                        { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                    ],
                },
                Tumbler: {
                    colors: [
                        { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                        { name: "Brushed Steel", class: "checkbox-brushed_steel", category: "Neutral Colors" },
                        { name: "White", class: "checkbox-white", category: "Light Colors" },
                    ],
                },
            },
        },
        y = {
            products: {
                STANDARD_TSHIRT: {
                    type: "clothing",
                    class: "product-card",
                    id: "STANDARD_TSHIRT-card",
                    editButtonClass: "STANDARD_TSHIRT-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                PREMIUM_TSHIRT: {
                    type: "clothing",
                    class: "product-card",
                    id: "PREMIUM_TSHIRT-card",
                    editButtonClass: "PREMIUM_TSHIRT-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                VNECK: {
                    type: "clothing",
                    class: "product-card",
                    id: "VNECK-card",
                    editButtonClass: "VNECK-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                TANK_TOP: {
                    type: "clothing",
                    class: "product-card",
                    id: "TANK_TOP-card",
                    editButtonClass: "TANK_TOP-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                STANDARD_LONG_SLEEVE: {
                    type: "clothing",
                    class: "product-card",
                    id: "STANDARD_LONG_SLEEVE-card",
                    editButtonClass: "STANDARD_LONG_SLEEVE-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                RAGLAN: {
                    type: "clothing",
                    class: "product-card",
                    id: "RAGLAN-card",
                    editButtonClass: "RAGLAN-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                STANDARD_SWEATSHIRT: {
                    type: "clothing",
                    class: "product-card",
                    id: "STANDARD_SWEATSHIRT-card",
                    editButtonClass: "STANDARD_SWEATSHIRT-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                STANDARD_PULLOVER_HOODIE: {
                    type: "clothing",
                    class: "product-card",
                    id: "STANDARD_PULLOVER_HOODIE-card",
                    editButtonClass: "STANDARD_PULLOVER_HOODIE-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                ZIP_HOODIE: {
                    type: "clothing",
                    class: "product-card",
                    id: "ZIP_HOODIE-card",
                    editButtonClass: "ZIP_HOODIE-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                POP_SOCKET: {
                    type: "scalable",
                    class: "product-card",
                    id: "POP_SOCKET-card",
                    editButtonClass: "POP_SOCKET-edit-btn",
                    productDetails: "product-editor",
                    colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
                    colorHex: "color-editable-input",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                PHONE_CASE_APPLE_IPHONE: {
                    type: "scalable",
                    class: "product-card",
                    id: "PHONE_CASE_APPLE_IPHONE-card",
                    editButtonClass: "PHONE_CASE_APPLE_IPHONE-edit-btn",
                    productDetails: "product-editor",
                    colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
                    colorHex: "color-editable-input",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                TOTE_BAG: {
                    type: "scalable",
                    class: "product-card",
                    id: "TOTE_BAG-card",
                    editButtonClass: "TOTE_BAG-edit-btn",
                    productDetails: "product-editor",
                    colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
                    colorHex: "color-editable-input",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                THROW_PILLOW: {
                    type: "scalable",
                    class: "product-card",
                    id: "THROW_PILLOW-card",
                    editButtonClass: "THROW_PILLOW-edit-btn",
                    productDetails: "product-editor",
                    colorPickerToggle: ["https://m.media-amazon.com/images/G/01/gear/designerapp/icons/icon_palette.svg", "btn btn-secondary icon"],
                    colorHex: "color-editable-input",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
                TUMBLER: {
                    type: "tumbler",
                    class: "product-card",
                    id: "TUMBLER-card",
                    editButtonClass: "TUMBLER-edit-btn",
                    productDetails: "product-editor",
                    colorClass: "color-groups-container",
                    parentPriceClass: "div[class='nav-container']",
                    marketplacePriceClass: "price-container",
                    marketplacePriceId: "form-row pl-3",
                    priceInput: "form-control pl-3",
                },
            },
        },
        x = [
            "STANDARD_TSHIRT",
            "PREMIUM_TSHIRT",
            "VNECK",
            "TANK_TOP",
            "STANDARD_LONG_SLEEVE",
            "RAGLAN",
            "STANDARD_SWEATSHIRT",
            "STANDARD_PULLOVER_HOODIE",
            "ZIP_HOODIE",
            "POP_SOCKET",
            "PHONE_CASE_APPLE_IPHONE",
            "TOTE_BAG",
            "THROW_PILLOW",
            "TUMBLER",
        ],
        P = { activeTab: localStorage.getItem("snap-active-tab") || "color-pricing", selectedProfile: null, quickColorMode: localStorage.getItem("snap-quick-color-mode") || "dark", profiles: [], isDropdownOpen: !1 },
        b = {
            async saveProfiles() {
                try {
                    await chrome.storage.local.set({ "snap-profiles": P.profiles, "snap-selected-profile": P.selectedProfile ? P.selectedProfile.name : null });
                } catch (e) {}
            },
            async loadProfiles() {
                try {
                    var e,
                        t = await chrome.storage.local.get(["snap-profiles", "snap-selected-profile"]);
                    P.profiles = t["snap-profiles"] || [];
                    const r = t["snap-selected-profile"];
                    0 < P.profiles.length ? (r ? ((e = P.profiles.find((e) => e.name === r)), (P.selectedProfile = e || P.profiles[0])) : (P.selectedProfile = P.profiles[0])) : (P.selectedProfile = null),
                        v.elements &&
                            (v.elements.dropdown?.value && (v.elements.dropdown.value.textContent = P.selectedProfile ? P.selectedProfile.name : "Select a profile"),
                            v.elements.dropdown?.list && v.updateProfilesList(),
                            v.updateActionButtons(),
                            v.updateExecuteButtonState(),
                            v.updateTabUI(),
                            v.updateSectionVisibility(),
                            v.updateQuickColorTabs());
                } catch (e) {}
            },
        },
        v = {
            elements: {},
            productOrder: [
                "STANDARD_TSHIRT",
                "PREMIUM_TSHIRT",
                "VNECK",
                "TANK_TOP",
                "STANDARD_LONG_SLEEVE",
                "RAGLAN",
                "STANDARD_SWEATSHIRT",
                "STANDARD_PULLOVER_HOODIE",
                "ZIP_HOODIE",
                "POP_SOCKET",
                "PHONE_CASE_APPLE_IPHONE",
                "TOTE_BAG",
                "THROW_PILLOW",
                "TUMBLER",
            ],
            init() {
                try {
                    this.cacheElements(), this.validateElements() && (this.bindEvents(), this.updateEmptyState(), this.checkApplyColorsCTAState(), this.observeSubmitButton());
                } catch (e) {}
            },
            cacheElements() {
                (this.elements = {
                    container: document.querySelector(".snap-profile-container"),
                    tabs: { colorPricing: document.querySelector('[data-tab="color-pricing"]'), quickColor: document.querySelector('[data-tab="quick-color"]') },
                    sections: { colorProfiles: document.getElementById("color-profiles-container"), quickTabs: document.getElementById("quick-tabs-container") },
                    containers: { executeAction: document.querySelector(".execute-action-container"), applyColors: document.querySelector(".apply-colors-container") },
                    dropdown: {
                        container: document.querySelector(".select-dropdown"),
                        value: document.getElementById("selectedValue"),
                        menu: document.getElementById("dropdownMenu"),
                        list: document.getElementById("dropdownList"),
                        search: document.getElementById("searchInput"),
                    },
                    buttons: {
                        executeAction: document.getElementById("executeActionButton"),
                        applyColors: document.getElementById("applyColorsButton"),
                        create: document.querySelector('[data-tooltip="Create Profile"]'),
                        import: document.querySelector('[data-tooltip="Import Profile"]'),
                        export: document.getElementById("export-profile-btn"),
                        delete: document.getElementById("deleteIcon"),
                    },
                    popup: {
                        container: document.getElementById("createProfilePopup"),
                        close: document.querySelector(".popup-close"),
                        input: document.getElementById("profileNameInput"),
                        createBtn: document.getElementById("createProfileButton"),
                        errorMsg: document.querySelector(".error-message"),
                    },
                    quickColorTabs: {
                        dark: document.getElementById("dark-colors-tab"),
                        light: document.getElementById("light-colors-tab"),
                        all: document.getElementById("all-colors-tab"),
                        clear: document.getElementById("clear-colors-tab"),
                    },
                }),
                    console.log("", {});
            },
            validateElements() {
                return (
                    this.elements.container &&
                    this.elements.tabs.colorPricing &&
                    this.elements.tabs.quickColor &&
                    this.elements.sections.colorProfiles &&
                    this.elements.sections.quickTabs &&
                    this.elements.containers.executeAction &&
                    this.elements.containers.applyColors &&
                    this.elements.dropdown.container &&
                    this.elements.dropdown.value &&
                    this.elements.dropdown.menu &&
                    this.elements.dropdown.list &&
                    this.elements.dropdown.search
                );
            },
            bindEvents() {
                this.elements.tabs.colorPricing.addEventListener("click", () => this.switchTab("color-pricing")), this.elements.tabs.quickColor.addEventListener("click", () => this.switchTab("quick-color"));
                const t = this.elements.dropdown.container;
                var e;
                t &&
                    (t.addEventListener("click", (e) => {
                        e.stopPropagation(), this.toggleDropdown();
                    }),
                    (e = this.elements.dropdown.search) && (e.addEventListener("click", (e) => e.stopPropagation()), e.addEventListener("input", (e) => this.handleSearch(e))),
                    document.addEventListener("click", (e) => {
                        !t.contains(e.target) && P.isDropdownOpen && ((P.isDropdownOpen = !1), this.updateDropdownUI());
                    })),
                    [...document.querySelectorAll('.custom-dotted[data-tooltip="Create Profile"]'), ...document.querySelectorAll(".create-profile-button")].forEach((e) => {
                        e &&
                            e.addEventListener("click", () => {
                                this.showCreatePopup();
                            });
                    }),
                    document.querySelectorAll('.custom-dotted[data-tooltip="Import Profile"]').forEach((e) => {
                        e &&
                            e.addEventListener("click", () => {
                                this.importProfile();
                            });
                    }),
                    this.elements.buttons.executeAction &&
                        this.elements.buttons.executeAction.addEventListener("click", () => {
                            this.executeAction();
                        }),
                    this.elements.buttons.export?.addEventListener("click", () => this.exportProfile()),
                    this.elements.buttons.delete?.addEventListener("click", () => this.deleteProfile()),
                    this.elements.buttons.applyColors?.addEventListener("click", () => this.applyColors()),
                    this.elements.popup.close?.addEventListener("click", () => this.hideCreatePopup()),
                    this.elements.popup.input?.addEventListener("input", () => this.validateProfileName()),
                    this.elements.popup.createBtn?.addEventListener("click", () => this.createProfile()),
                    this.elements.popup.container?.addEventListener("click", (e) => {
                        e.target === this.elements.popup.container && this.hideCreatePopup();
                    }),
                    Object.values(this.elements.quickColorTabs).forEach((e) => {
                        e && e.addEventListener("click", () => this.switchQuickColorTab(e.id));
                    });
            },
            updateTabUI() {
                const { colorPricing: r, quickColor: e } = this.elements.tabs,
                    o = "color-pricing" === P.activeTab;
                [r, e].forEach((e) => {
                    var t = (e === r) == o;
                    (e.style.backgroundColor = t ? "#470CED" : "white"), (e.style.color = t ? "white" : "#470CED"), e.querySelector("img").classList.toggle("white-icon", t);
                });
            },
            updateSectionVisibility() {
                var e = "color-pricing" === P.activeTab,
                    { colorProfiles: t, quickTabs: r } = this.elements.sections,
                    { executeAction: o, applyColors: a } = this.elements.containers;
                t.classList.toggle("hidden", !e), r.classList.toggle("hidden", e), o.classList.toggle("hidden", !e), a.classList.toggle("hidden", e);
            },
            updateProfilesList() {
                try {
                    const o = this.elements.dropdown["list"];
                    o &&
                        ((o.innerHTML = ""),
                        P.profiles.forEach((t) => {
                            var e = document.createElement("div"),
                                r = ((e.className = "dropdown-item"), P.selectedProfile && t.name === P.selectedProfile.name);
                            r && e.classList.add("selected"),
                                (e.textContent = t.name),
                                (e.onclick = (e) => {
                                    e.stopPropagation(), this.selectProfile(t);
                                }),
                                o.appendChild(e);
                        }),
                        this.updateEmptyState());
                } catch (e) {}
            },
            updateEmptyState() {
                try {
                    var e,
                        t = document.getElementById("profilesListState"),
                        r = document.getElementById("emptyState");
                    t && r && ((e = 0 < P.profiles.length), t.classList.toggle("hidden", !e), r.classList.toggle("hidden", e));
                } catch (e) {}
            },
            updateDropdownUI() {
                var { menu: e, container: t } = this.elements.dropdown;
                e && t && (e.classList.toggle("hidden", !P.isDropdownOpen), t.classList.toggle("focused", P.isDropdownOpen));
            },
            updateActionButtons() {
                var { export: e, delete: t } = this.elements.buttons;
                const r = !!P.selectedProfile;
                [e, t].forEach((e) => {
                    e.classList.toggle("disabled", !r), e.querySelector("img").classList.toggle("disabled-icon", !r);
                });
            },
            updateExecuteButtonState() {
                var e = "color-pricing" === P.activeTab,
                    { executeAction: t, applyColors: r } = v.elements.buttons;
                e ? ((e = !!P.selectedProfile), t.classList.toggle("disabled", !e)) : ((t = this.hasEnabledColorProducts()), r.classList.toggle("disabled", !t), (r.querySelector("img").style.filter = t ? "" : "brightness(0) opacity(0.5)"));
            },
            updateQuickColorTabs() {
                try {
                    this.elements.quickColorTabs &&
                        Object.entries(this.elements.quickColorTabs).forEach(([e, t]) => {
                            t && ((e = P.quickColorMode === e), t.classList.toggle("active", e), (t = t.querySelector("img"))) && (t.classList.toggle("blue-icon", e), t.classList.toggle("gray-icon", !e));
                        });
                } catch (e) {}
            },
            createProgressBar(e = "Applying Colors") {
                var t = document.createElement("div"),
                    e =
                        ((t.className = "snap-progress-bar"),
                        (t.innerHTML = `
                <div class="progress-content">
                    <div class="progress-text">${e}</div>
                    <div class="percentage-text">0%</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar-background"></div>
                        <div class="current-progress"></div>
                    </div>
                    <div class="progress-subtext">Please wait..</div>
                </div>
            `),
                        document.createElement("style"));
                return (
                    (e.textContent = `
                @font-face {
                    font-family: 'Amazon Ember';
                    src: url('chrome-extension://${chrome.runtime.id}/fonts/AmazonEmber_Rg.ttf') format('truetype');
                }

                .snap-progress-bar {
                    position: fixed;
                    bottom: 100px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 330px;
                    height: 100px;
                    background-color: #470CED;
                    border-radius: 9px;
                    z-index: 10000;
                }

                .progress-content {
                    padding: 27.78px;
                    color: white;
                    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                }

                .progress-text {
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 10px;
                    line-height: 10px;
                }

                .percentage-text {
                    position: absolute;
                    right: 27.78px;
                    top: 27.78px;
                    font-size: 12px;
                    line-height: 8px;
                }

                .progress-bar-container {
                    position: relative;
                    height: 5px;
                    margin: 19.5px 0 9px;
                }

                .progress-bar-background {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    background-color: #F7F8FA;
                    border-radius: 10px;
                }

                .current-progress {
                    position: absolute;
                    width: 0%;
                    height: 100%;
                    background-color: #01BB87;
                    border-radius: 10px;
                    transition: width 0.3s ease;
                }

                .progress-subtext {
                    font-size: 12px;
                    line-height: 8px;
                    opacity: 0.8;
                }

                button:focus,
                input:focus {
                    outline: none !important;
                }
            `),
                    document.head.appendChild(e),
                    t
                );
            },
            hasEnabledColorProducts() {
                for (var [e, t] of Object.entries(y.products))
                    if ("clothing" === t.type || "tumbler" === t.type) {
                        t = document.querySelector(`#${e}-card`);
                        if (t && !t.classList.contains("disabled")) return !0;
                    }
                return !1;
            },
            observeSubmitButton() {
                const t = (e) => {
                    e &&
                        !e.hasSubmitListener &&
                        ((e.hasSubmitListener = !0),
                        e.addEventListener("click", () => {
                            this.checkApplyColorsCTAState();
                        }));
                };
                var e = document.querySelector('div[class="modal-dialog modal-lg"]');
                e && ((e = e.querySelector(".btn.btn-primary.btn-submit")), t(e)),
                    new MutationObserver((e) => {
                        e.forEach((e) => {
                            "childList" === e.type && (e = document.querySelector('div[class="modal-dialog modal-lg"]')) && (e = e.querySelector(".btn.btn-primary.btn-submit")) && t(e);
                        });
                    }).observe(document.body, { childList: !0, subtree: !0 });
            },
            handleClickOutside(e) {
                var t = v.elements["dropdown"];
                !t.container.contains(e.target) && P.isDropdownOpen && ((P.isDropdownOpen = !1), v.updateDropdownUI());
            },
            handleSearch(e) {
                const r = e.target.value.toLowerCase().trim();
                v.elements.dropdown.list.querySelectorAll(".dropdown-item").forEach((e) => {
                    var t = e.textContent.toLowerCase().includes(r);
                    e.style.display = t ? "" : "none";
                });
            },
            async switchTab(e) {
                var t, r;
                (P.activeTab = e),
                    localStorage.setItem("snap-active-tab", e),
                    v.updateTabUI(),
                    v.updateSectionVisibility(),
                    "color-pricing" === e
                        ? ((t = 0 < P.profiles.length), (r = v.elements.buttons.executeAction) && (r.classList.toggle("disabled", !t), (r = r.querySelector("img"))) && (r.style.filter = t ? "" : "brightness(0) opacity(0.5)"))
                        : "quick-color" === e && v.checkApplyColorsCTAState();
            },
            toggleDropdown() {
                (P.isDropdownOpen = !P.isDropdownOpen), v.updateDropdownUI();
            },
            selectProfile(e) {
                (P.selectedProfile = e), (v.elements.dropdown.value.textContent = e.name), (P.isDropdownOpen = !1), v.updateDropdownUI(), v.updateProfilesList(), v.updateActionButtons(), v.updateExecuteButtonState(), b.saveProfiles();
            },
            showCreatePopup() {
                const { container: e, input: t } = v.elements.popup;
                (e.style.display = "flex"),
                    (t.value = ""),
                    setTimeout(() => {
                        t.focus();
                    }, 50),
                    this.validateProfileName();
            },
            hideCreatePopup() {
                var { container: e, input: t, createBtn: r, errorMsg: o } = v.elements.popup;
                (e.style.display = "none"), (t.value = ""), r.classList.remove("active"), (o.style.display = "none");
            },
            validateProfileName() {
                var { input: e, createBtn: t, errorMsg: r } = v.elements.popup;
                const o = e.value.trim();
                t.classList.remove("active"),
                    (r.style.display = "none"),
                    o &&
                        (30 < o.length
                            ? ((r.textContent = "Profile name cannot exceed 30 characters"), (r.style.display = "block"))
                            : P.profiles.some((e) => e.name.toLowerCase() === o.toLowerCase())
                            ? ((r.textContent = "This profile name already exists"), (r.style.display = "block"))
                            : t.classList.add("active"));
            },
            async createProfile() {
                var { input: r, createBtn: e } = v.elements.popup;
                if (e.classList.contains("active")) {
                    let t;
                    var o,
                        a = async (e, t) => {
                            await new Promise((e) => setTimeout(e, 100)), await e(), await new Promise((e) => setTimeout(e, 100));
                        };
                    try {
                        var s = { name: r.value.trim(), createdAt: new Date().toISOString() };
                        if ((this.hideCreatePopup(), await new Promise((e) => setTimeout(e, 300)), !(await w()))) throw new Error("Could not achieve clean starting state");
                        (t = v.createProgressBar("Creating Profile")), document.body.appendChild(t), v.updateProgress(t, 0);
                        const n = document.querySelector("button#select-marketplace-button-original");
                        if (!n) throw new Error("Marketplace button not found");
                        await a(async () => n.click()), await new Promise((e) => setTimeout(e, 1e3));
                        var c = document.querySelector(".select-products-table");
                        if (!c) throw new Error("Product table not found");
                        if (
                            ((o = await (async function (e) {
                                const o = {};
                                try {
                                    var t = e.querySelectorAll("tr");
                                    const a = Array.from(t[0]?.querySelectorAll("th") || [])
                                        .map((e) => e.textContent.trim())
                                        .filter((e) => e.match(/\.(com|co\.uk|de|fr|it|es|co\.jp)$/));
                                    return (
                                        t.forEach((e, t) => {
                                            if (0 !== t) {
                                                t = e.querySelector("td:first-child");
                                                if (t) {
                                                    t = t.textContent.trim();
                                                    if (t) {
                                                        const r = C.getEnglishName(t);
                                                        r &&
                                                            ((o[r] = {}),
                                                            e.querySelectorAll('input[type="checkbox"]').forEach((e, t) => {
                                                                a[t] && (o[r][a[t]] = e.checked);
                                                            }));
                                                    }
                                                }
                                            }
                                        }),
                                        o
                                    );
                                } catch (e) {
                                    throw new Error("Failed to capture marketplace states");
                                }
                            })(c)),
                            0 === Object.keys(o).length)
                        )
                            throw new Error("No marketplace states were captured");
                        await a(async () => {
                            var e = document.querySelector('button.btn.btn-primary.btn-submit[type="button"]');
                            e && e.click();
                        }),
                            v.updateProgress(t, 10);
                        const d = { ProfileGeneratedBy: "Snap for Merch on Demand", name: s.name, createdAt: s.createdAt, marketplaceStates: o, products: {} };
                        for (const u of x) d.products[u] = {};
                        let e = 0;
                        var i = 80 / v.productOrder.length;
                        for (const h of v.productOrder)
                            try {
                                var l = document.querySelector(`div[class*="product-card"][id*="${h}"]`);
                                if (!l || l.classList.contains("disabled"));
                                else {
                                    const g = l.querySelector(`button[class*="${h}-edit-btn"]`);
                                    if (g) {
                                        if (0 === e) {
                                            let e = null !== document.querySelector(".nav-container")?.offsetParent;
                                            if (
                                                (e
                                                    ? (g.click(),
                                                      g.blur(),
                                                      await new Promise((e) => setTimeout(e, 500)),
                                                      (e = null !== document.querySelector(".nav-container")?.offsetParent) ||
                                                          (g.click(), g.blur(), await new Promise((e) => setTimeout(e, 500)), (e = null !== document.querySelector(".nav-container")?.offsetParent)))
                                                    : (g.click(), g.blur(), await new Promise((e) => setTimeout(e, 500)), (e = null !== document.querySelector(".nav-container")?.offsetParent)),
                                                e || (g.click(), g.blur(), await new Promise((e) => setTimeout(e, 500)), (e = null !== document.querySelector(".nav-container")?.offsetParent)),
                                                !document.querySelector(".nav-container")?.offsetParent)
                                            )
                                                continue;
                                        } else
                                            await a(async () => {
                                                g.click(), await new Promise((e) => setTimeout(e, 200)), this.isScalableProduct(h) && (await new Promise((e) => setTimeout(e, 150)));
                                            }, h);
                                        const m = { colors: [], prices: [] };
                                        await a(async () => {
                                            var e;
                                            this.isScalableProduct(h)
                                                ? (e = document.querySelector("button#color-btn.btn.btn-secondary.icon")) &&
                                                  (e.click(), await new Promise((e) => setTimeout(e, 300)), document.querySelector(".sketch-picker")) &&
                                                  (e = window.getComputedStyle(e).backgroundColor) &&
                                                  (e = e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)) &&
                                                  ((e =
                                                      "#" +
                                                      e
                                                          .slice(1)
                                                          .map((e) => parseInt(e, 10).toString(16).padStart(2, "0"))
                                                          .join("")
                                                          .toUpperCase()),
                                                  m.colors.push(e))
                                                : (e = document.querySelector(".color-group-container")) &&
                                                  e.querySelectorAll(".color-checkbox-container .sci-icon.sci-check.checkmark").forEach((e) => {
                                                      var e = e.closest(".color-checkbox");
                                                      e && (e = Array.from(e.classList).find((e) => e.startsWith("checkbox-"))) && ((e = e.replace("checkbox-", "")), m.colors.push(e));
                                                  });
                                        }, h),
                                            await a(async () => {
                                                for (const r of document.querySelectorAll(".product-editor .price-container.p-small.border-top.d-block")) {
                                                    var e = r.querySelector("strong")?.textContent?.trim(),
                                                        t = r.querySelector('input.form-control.pl-3[formcontrolname="amount"]');
                                                    e && t && t.value && ((t = parseFloat(t.value)), isNaN(t) || (m.prices.push({ marketplace: e, price: t }), await new Promise((e) => setTimeout(e, 100))));
                                                }
                                            }, h),
                                            (0 < m.colors.length || 0 < m.prices.length) && (d.products[h] = m);
                                    }
                                }
                                e++, v.updateProgress(t, 10 + e * i);
                            } catch (e) {}
                        if (!(0 < Object.keys(d.products).length)) throw new Error("No product data was captured");
                        await a(async () => {
                            P.profiles.push(d), await b.saveProfiles(), v.updateProfilesList(), this.selectProfile(d);
                        }),
                            v.updateProgress(t, 100);
                        const p = `Profile "${d.name}" has been created successfully.`;
                        setTimeout(() => {
                            t &&
                                t.parentNode &&
                                (t.remove(),
                                setTimeout(() => {
                                    window.alert(p);
                                }, 100));
                        }, 1e3);
                    } catch (e) {
                        t && t.parentNode && t.remove(), window.alert(e.message || "Failed to create profile. Please try again.");
                    }
                }
            },
            async waitForElement(e, t = 5e3) {
                for (var r = Date.now(); Date.now() - r < t; ) {
                    var o = document.querySelector(e);
                    if (o) return o;
                    await new Promise((e) => setTimeout(e, 100));
                }
                return null;
            },
            isScalableProduct(e) {
                return ["POP_SOCKET", "PHONE_CASE_APPLE_IPHONE", "TOTE_BAG", "THROW_PILLOW"].includes(e);
            },
            extractColorName(e) {
                return e.replace("checkbox-", "").replace(/_/g, " ");
            },
            async importProfile() {
                var e = document.createElement("input");
                (e.type = "file"),
                    (e.accept = ".json"),
                    (e.multiple = !0),
                    (e.onchange = async (e) => {
                        var t = Array.from(e.target.files);
                        let r = null,
                            o = 0,
                            a = 0,
                            s = 0;
                        for (const n of t)
                            try {
                                var c = await new Promise((r, o) => {
                                    var e = new FileReader();
                                    (e.onload = (e) => {
                                        try {
                                            var t = JSON.parse(e.target.result);
                                            r(t);
                                        } catch (e) {
                                            o(new Error("Invalid JSON file"));
                                        }
                                    }),
                                        (e.onerror = () => o(new Error("Failed to read file"))),
                                        e.readAsText(n);
                                });
                                if (c.ProfileGeneratedBy && "Snap for Merch on Demand" === c.ProfileGeneratedBy) {
                                    var i = n.name.replace(".json", ""),
                                        l = i.split("_");
                                    let t;
                                    30 < (t = 3 <= l.length && "Snap" === l[0] && "for" === l[1] && "Merch" === l[2] ? l.slice(3, -1).join("_") : i).length
                                        ? s++
                                        : P.profiles.some((e) => e.name === t)
                                        ? a++
                                        : ((c.name = t), P.profiles.push(c), (r = c), o++);
                                } else {
                                    if (1 === t.length) return void window.alert("This is not a valid Snap for Merch on Demand profile");
                                    s++;
                                }
                            } catch (e) {
                                if (1 === t.length) return void window.alert("This is not a valid Snap for Merch on Demand profile");
                                s++;
                            }
                        1 < t.length &&
                            (0 < o || 0 < a || 0 < s) &&
                            ((e = []),
                            0 < o && e.push(`Successfully imported ${o} profile` + (1 < o ? "s" : "")),
                            0 < a && e.push(`${a} profile${1 < a ? "s were" : " was"} skipped because ` + (1 < a ? "they already exist" : "it already exists")),
                            0 < s && e.push(`${s} file${1 < s ? "s" : ""} skipped because ${1 < s ? "they are" : "it is"} not valid Snap for Merch on Demand profile` + (1 < s ? "s" : "")),
                            window.alert(e.join("\n"))),
                            0 < o && (await b.saveProfiles(), v.updateProfilesList(), r) && this.selectProfile(r);
                    }),
                    e.click();
            },
            exportProfile() {
                if (P.selectedProfile) {
                    var e = { ProfileGeneratedBy: P.selectedProfile.ProfileGeneratedBy, name: P.selectedProfile.name, createdAt: P.selectedProfile.createdAt, marketplaceStates: P.selectedProfile.marketplaceStates, products: {} };
                    for (const o of x) P.selectedProfile.products[o] && (e.products[o] = P.selectedProfile.products[o]);
                    var t = new Blob([JSON.stringify(e, null, 2)], { type: "application/json" }),
                        t = URL.createObjectURL(t),
                        r = document.createElement("a");
                    (r.href = t), (r.download = P.selectedProfile.name + "_Snap.json"), document.body.appendChild(r), r.click(), document.body.removeChild(r), URL.revokeObjectURL(t);
                }
            },
            async deleteProfile() {
                if (P.selectedProfile) {
                    var t = `Are you sure you want to delete "${P.selectedProfile.name}"?`;
                    if (window.confirm(t)) {
                        const o = P.selectedProfile.name;
                        if (((P.profiles = P.profiles.filter((e) => e.name !== o)), 0 < P.profiles.length)) {
                            var t = [...P.profiles].sort((e, t) => e.name.localeCompare(t.name)),
                                r = t.findIndex((e) => e.name === o);
                            let e;
                            (e = -1 === r || r >= t.length ? t[t.length - 1] : t[r] || t[0]), (P.selectedProfile = e), (v.elements.dropdown.value.textContent = e.name);
                        } else (P.selectedProfile = null), (v.elements.dropdown.value.textContent = "Select a profile");
                        await b.saveProfiles(), v.updateProfilesList(), v.updateActionButtons(), v.updateExecuteButtonState();
                    }
                }
            },
            async switchQuickColorTab(e) {
                e = e.replace("-colors-tab", "");
                (P.quickColorMode = e), localStorage.setItem("snap-quick-color-mode", e), v.updateQuickColorTabs(), v.checkApplyColorsCTAState();
            },
            executeAction() {
                v.elements.buttons.executeAction.classList.contains("disabled") || ("color-pricing" === P.activeTab && P.selectedProfile && v.executeProfile());
            },
            applyColors() {
                var e = v.createProgressBar(),
                    t = (document.body.appendChild(e), P.quickColorMode);
                this.processProducts(t, e);
            },
            getEnabledProducts(e) {
                var t = [];
                for (const o of e) {
                    var r = document.querySelector(`#${o}-card`);
                    r && !r.classList.contains("disabled") && t.push(o);
                }
                return t;
            },
            async processProducts(t, r) {
                try {
                    var e = [
                            "STANDARD_TSHIRT",
                            "PREMIUM_TSHIRT",
                            "VNECK",
                            "TANK_TOP",
                            "STANDARD_LONG_SLEEVE",
                            "RAGLAN",
                            "STANDARD_SWEATSHIRT",
                            "STANDARD_PULLOVER_HOODIE",
                            "ZIP_HOODIE",
                            "POP_SOCKET",
                            "PHONE_CASE_APPLE_IPHONE",
                            "TOTE_BAG",
                            "THROW_PILLOW",
                            "TUMBLER",
                        ].filter((e) => {
                            e = y.products[e];
                            return e && ("clothing" === e.type || "tumbler" === e.type);
                        }),
                        o = this.getEnabledProducts(e);
                    if (0 === o.length) r.remove();
                    else {
                        o.forEach((e) => {
                            this.mapConfigKeyToProfileKey(e);
                        });
                        var a = o[0];
                        const l = y.products[a];
                        var s = document.querySelector("." + l.editButtonClass);
                        if (s) {
                            const n = () => {
                                var e = document.querySelector("." + l.colorClass);
                                return e && null !== e.offsetParent;
                            };
                            var c = async (e = 400) => {
                                for (var t = Date.now(); Date.now() - t < e; ) {
                                    if (n()) return !0;
                                    await new Promise((e) => setTimeout(e, 50));
                                }
                                return !1;
                            };
                            let e = n();
                            if (((e && (s.click(), s.blur(), await new Promise((e) => setTimeout(e, 500)), (e = n()))) || (s.click(), s.blur(), (e = await c())), e || (s.click(), s.blur(), (e = await c())), n())) {
                                await this.processProduct(a, t, r, 1, o.length), document.body.click(), await new Promise((e) => setTimeout(e, 300));
                                for (let e = 1; e < o.length; e++) {
                                    var i = o[e];
                                    await this.processProduct(i, t, r, e + 1, o.length);
                                }
                                this.updateProgress(r, 100),
                                    await new Promise((e) => setTimeout(e, 300)),
                                    setTimeout(() => {
                                        r.remove();
                                    }, 1e3);
                            } else r.remove();
                        } else r.remove();
                    }
                } catch (e) {
                    r && r.remove();
                }
            },
            async processProduct(e, t, r, o, a) {
                if ("dark" !== t || "RAGLAN" !== e) {
                    var s = this.mapConfigKeyToProfileKey(e),
                        c = y.products[e].editButtonClass,
                        c = document.querySelector("." + c);
                    if (c) {
                        var i = document.querySelector("." + y.products[e].colorClass),
                            l = 1 === o,
                            n = ((l && i) || (c.click(), c.blur(), await new Promise((e) => setTimeout(e, 500))), document.querySelector("." + y.products[e].colorClass));
                        if (n) {
                            var d,
                                c = u.products[s]?.colors || [];
                            console.log("", {}),
                                n.querySelectorAll('input[type="checkbox"]').forEach((e) => {
                                    e.checked && (e.click(), e.blur());
                                });
                            for (const p of c) this.shouldApplyColor(p.category, t) && (d = n.querySelector("." + p.class)) && !d.checked && (d.click(), d.blur());
                            this.updateProgress(r, (o / a) * 100), (l && i) || (document.body.click(), await new Promise((e) => setTimeout(e, 300)));
                        }
                    }
                }
            },
            updateProgress(e, t) {
                var r = e.querySelector(".current-progress"),
                    e = e.querySelector(".percentage-text"),
                    t = Math.round(t);
                (r.style.width = t + "%"), (e.textContent = t + "%");
            },
            shouldApplyColor(e, t) {
                let r = !1;
                switch (t.replace("-colors", "").replace("-btn", "")) {
                    case "dark":
                        r = "Dark Colors" === e || "Neutral Colors" === e;
                        break;
                    case "light":
                        r = "Light Colors" === e || "Neutral Colors" === e;
                        break;
                    case "all":
                        r = !0;
                        break;
                    default:
                        r = !1;
                }
                return console.log("", {}), r;
            },
            mapConfigKeyToProfileKey(e) {
                return (
                    {
                        STANDARD_TSHIRT: "Standard T-shirt",
                        PREMIUM_TSHIRT: "Premium T-shirt",
                        STANDARD_SWEATSHIRT: "Sweatshirt",
                        RAGLAN: "Raglan",
                        STANDARD_LONG_SLEEVE: "Long Sleeve T-shirt",
                        TANK_TOP: "Tank Top",
                        VNECK: "V-neck T-shirt",
                        ZIP_HOODIE: "Zip Hoodie",
                        STANDARD_PULLOVER_HOODIE: "Pullover Hoodie",
                        POP_SOCKET: "Pop Socket",
                        PHONE_CASE_APPLE_IPHONE: "iPhone Case",
                        TOTE_BAG: "Tote Bag",
                        THROW_PILLOW: "Throw Pillow",
                        TUMBLER: "Tumbler",
                    }[e] || e
                );
            },
            checkApplyColorsCTAState() {
                var e, t;
                "quick-color" === P.activeTab &&
                    ((e = this.hasEnabledColorProducts()), (t = this.elements.buttons["applyColors"]), t.classList.toggle("disabled", !e), (t.querySelector("img").style.filter = e ? "" : "brightness(0) opacity(0.5)"));
            },
            isColorSelected(e) {
                return null !== e.querySelector(".sci-icon.sci-check.checkmark");
            },
            handleColorSelection(e, l) {
                e = e.querySelectorAll(".color-checkbox-container");
                const a = new Map();
                e.forEach((e) => {
                    var t,
                        r = e.querySelector("colorcheckbox");
                    if (r) {
                        const o = Array.from(r.classList)
                            .find((e) => e.endsWith("-checkbox"))
                            ?.replace("-checkbox", "");
                        o &&
                            (r = e.querySelector('span[class*="color-checkbox"]')) &&
                            (e = this.productcolorProfiles.colors.find((e) => e.class === "checkbox-" + o || e.name.toLowerCase().replace(/\s+/g, "_") === o)) &&
                            ((t = null !== r.querySelector(".sci-icon.sci-check.checkmark")), a.set(e.name, { container: r, info: e, currentlySelected: t }));
                    }
                }),
                    a.forEach(({ container: e, info: t, currentlySelected: r }, o) => {
                        var a = "Neutral Colors" === t.category,
                            s = "Dark Colors" === t.category,
                            c = "Light Colors" === t.category;
                        let i = r;
                        switch (l) {
                            case "dark":
                                s || a ? (i = !0) : c && (i = !1);
                                break;
                            case "light":
                                c || a ? (i = !0) : s && (i = !1);
                                break;
                            case "all":
                                i = !0;
                                break;
                            case "clear":
                                i = !1;
                        }
                        r !== i && e.click();
                    });
            },
            async executeProfile() {
                if (P.selectedProfile) {
                    const i = v.createProgressBar("Applying Profile");
                    document.body.appendChild(i);
                    var t = async (e, t) => {
                        await new Promise((e) => setTimeout(e, 100)), await e(), await new Promise((e) => setTimeout(e, 100));
                    };
                    try {
                        if (!(await w())) throw new Error("Could not achieve clean starting state");
                        v.updateProgress(i, 5);
                        const l = document.querySelector("button#select-marketplace-button-original");
                        if (l) {
                            await t(async () => l.click()), await new Promise((e) => setTimeout(e, 1e3));
                            const n = document.querySelector(".select-products-table");
                            if (!n) throw new Error("Product table not found");
                            if (P.selectedProfile.marketplaceStates) {
                                await t(async () => {
                                    (async function (e, o) {
                                        try {
                                            var t = e.querySelectorAll("tr");
                                            const a = Array.from(t[0]?.querySelectorAll("th") || [])
                                                .map((e) => e.textContent.trim())
                                                .filter((e) => e.match(/\.(com|co\.uk|de|fr|it|es|co\.jp)$/));
                                            t.forEach((e, t) => {
                                                if (0 !== t) {
                                                    t = e.querySelector("td:first-child");
                                                    if (t) {
                                                        (t = t.textContent.trim()), (t = C.getEnglishName(t));
                                                        if (t) {
                                                            const r = o[t];
                                                            r &&
                                                                e.querySelectorAll('input[type="checkbox"]').forEach((e, t) => {
                                                                    a[t] && void 0 !== r[a[t]] && e.checked !== r[a[t]] && (e.click(), e.blur());
                                                                });
                                                        }
                                                    }
                                                }
                                            });
                                        } catch (e) {
                                            throw new Error("Failed to apply marketplace states");
                                        }
                                    })(n, P.selectedProfile.marketplaceStates);
                                }),
                                    await new Promise((e) => setTimeout(e, 500));
                                const d = document.querySelector('button.btn.btn-primary.btn-submit[type="button"]');
                                d && (await t(async () => d.click()));
                            }
                        }
                        v.updateProgress(i, 10);
                        let e = 0;
                        var r = 80 / x.length;
                        for (const p of x)
                            try {
                                const u = P.selectedProfile.products[p];
                                if (u) {
                                    var o = y.products[p].editButtonClass;
                                    const h = document.querySelector("." + o);
                                    if (!(!h || h.disabled || h.classList.contains("disabled") || h.hasAttribute("disabled") || h.closest('div[class*="product-card"]')?.classList.contains("disabled"))) {
                                        if ("STANDARD_LONG_SLEEVE" === p || "ZIP_HOODIE" === p) {
                                            await t(async () => {
                                                h.click(), h.blur();
                                            }, p);
                                            var a = async (e = 1e3) => {
                                                for (var t = Date.now(); Date.now() - t < e; ) {
                                                    var r = document.querySelector(".color-group-container");
                                                    if (r && null !== r.offsetParent && 0 < r.querySelectorAll(".color-checkbox-container").length) {
                                                        r = r.querySelector(".color-checkbox");
                                                        if (r && !r.disabled) return !0;
                                                    }
                                                    await new Promise((e) => setTimeout(e, 50));
                                                }
                                                return !1;
                                            };
                                            if (!(await a(2e3)) && (h.click(), await new Promise((e) => setTimeout(e, 500)), h.click(), !(await a(2e3)))) continue;
                                            await new Promise((e) => setTimeout(e, 300));
                                        } else
                                            await t(async () => {
                                                h.click(), await new Promise((e) => setTimeout(e, 200)), this.isScalableProduct(p) && (await new Promise((e) => setTimeout(e, 150)));
                                            }, p);
                                        if ((this.isScalableProduct(p) && (await new Promise((e) => setTimeout(e, 150))), this.isScalableProduct(p))) {
                                            if (u.colors && 0 < u.colors.length) {
                                                const g = document.querySelector("button#color-btn.btn.btn-secondary.icon");
                                                if (g) {
                                                    await t(async () => g.click());
                                                    const m = document.querySelector(".sketch-picker .sketch-controls color-sketch-fields .sketch-double color-editable-input input");
                                                    if (m) {
                                                        const b = u.colors[0].replace("#", "");
                                                        await t(async () => {
                                                            m.focus(),
                                                                (m.value = b),
                                                                m.dispatchEvent(new Event("change", { bubbles: !0 })),
                                                                m.dispatchEvent(new Event("input", { bubbles: !0 })),
                                                                m.dispatchEvent(new KeyboardEvent("keyup", { key: "Enter", code: "Enter", keyCode: 13, bubbles: !0 }));
                                                            var e = window.getComputedStyle(g).backgroundColor;
                                                            e &&
                                                                (e = e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)) &&
                                                                "#" +
                                                                    e
                                                                        .slice(1)
                                                                        .map((e) => parseInt(e, 10).toString(16).padStart(2, "0"))
                                                                        .join("")
                                                                        .toUpperCase() !==
                                                                    u.colors[0].toUpperCase() &&
                                                                ((m.value = b.slice(0, -1)),
                                                                m.dispatchEvent(new Event("change", { bubbles: !0 })),
                                                                m.dispatchEvent(new Event("input", { bubbles: !0 })),
                                                                (m.value = b),
                                                                m.dispatchEvent(new Event("change", { bubbles: !0 })),
                                                                m.dispatchEvent(new Event("input", { bubbles: !0 })),
                                                                m.dispatchEvent(new KeyboardEvent("keydown", { key: "Enter", code: "Enter", keyCode: 13, bubbles: !0 })),
                                                                m.dispatchEvent(new KeyboardEvent("keyup", { key: "Enter", code: "Enter", keyCode: 13, bubbles: !0 })));
                                                        });
                                                    }
                                                }
                                            }
                                        } else {
                                            var s = document.querySelector(".color-group-container");
                                            if (s && u.colors) {
                                                const f = new Set(u.colors),
                                                    k = new Set();
                                                s.querySelectorAll(".color-checkbox-container .sci-icon.sci-check.checkmark").forEach((e) => {
                                                    var e = e.closest(".color-checkbox");
                                                    e && (e = Array.from(e.classList).find((e) => e.startsWith("checkbox-"))) && k.add(e.replace("checkbox-", ""));
                                                });
                                                var c = s.querySelectorAll(".color-checkbox-container .color-checkbox");
                                                c.forEach((e) => {
                                                    var t = Array.from(e.classList).find((e) => e.startsWith("checkbox-"));
                                                    t && ((t = t.replace("checkbox-", "")), (null !== e.querySelector(".sci-icon.sci-check.checkmark")) !== f.has(t)) && e.click();
                                                }),
                                                    c.forEach((e) => {
                                                        e.querySelector(".sci-icon.sci-check.checkmark") && e.click();
                                                    }),
                                                    c.forEach((e) => {
                                                        var t = Array.from(e.classList).find((e) => e.startsWith("checkbox-"));
                                                        t && ((t = t.replace("checkbox-", "")), f.has(t)) && e.click();
                                                    }),
                                                    await new Promise((e) => setTimeout(e, 100)),
                                                    await (async function (o, e, t = 2) {
                                                        if (
                                                            !(await (async (e = 1e3) => {
                                                                for (var t = Date.now(); Date.now() - t < e; ) {
                                                                    if (null !== o.offsetParent && 0 < o.querySelectorAll(".color-checkbox-container").length) {
                                                                        var r = o.querySelector(".color-checkbox");
                                                                        if (r && !r.disabled) return !0;
                                                                    }
                                                                    await new Promise((e) => setTimeout(e, 100));
                                                                }
                                                                return !1;
                                                            })())
                                                        )
                                                            return;
                                                        var r = () => {
                                                            const t = new Set();
                                                            return (
                                                                o.querySelectorAll(".color-checkbox-container .sci-icon.sci-check.checkmark").forEach((e) => {
                                                                    var e = e.closest(".color-checkbox");
                                                                    e && (e = Array.from(e.classList).find((e) => e.startsWith("checkbox-"))) && ((e = e.replace("checkbox-", "")), t.add(e));
                                                                }),
                                                                t
                                                            );
                                                        };
                                                        let a = 0,
                                                            s = !1;
                                                        for (; a <= t && !s; ) {
                                                            if (
                                                                (await new Promise((e) => setTimeout(e, 300)),
                                                                ((e, t) => {
                                                                    if (e.size !== t.size) return !1;
                                                                    for (const r of e) if (!t.has(r)) return !1;
                                                                    return !0;
                                                                })(r(), e))
                                                            ) {
                                                                s = !0;
                                                                break;
                                                            }
                                                            if (a < t) {
                                                                var c = o.querySelectorAll(".color-checkbox-container .color-checkbox");
                                                                for (const n of c) n.querySelector(".sci-icon.sci-check.checkmark") && (n.click(), await new Promise((e) => setTimeout(e, 100)));
                                                                await new Promise((e) => setTimeout(e, 300));
                                                                for (const d of c) {
                                                                    var i = Array.from(d.classList).find((e) => e.startsWith("checkbox-"));
                                                                    i && ((i = i.replace("checkbox-", "")), e.has(i)) && !d.querySelector(".sci-icon.sci-check.checkmark") && (d.click(), await new Promise((e) => setTimeout(e, 100)));
                                                                }
                                                                await new Promise((e) => setTimeout(e, 300));
                                                            }
                                                            a++;
                                                        }
                                                        var l = r();
                                                        Array.from(e).sort().join(", "), Array.from(l).sort().join(", ");
                                                        s;
                                                        return s;
                                                    })(s, f);
                                            }
                                        }
                                        u.prices &&
                                            0 < u.prices.length &&
                                            (await t(async () => {
                                                var e, t;
                                                for (const r of document.querySelectorAll(".product-editor .price-container.p-small.border-top.d-block")) {
                                                    const o = r.querySelector("strong")?.textContent?.trim();
                                                    o &&
                                                        (e = u.prices.find((e) => e.marketplace === o)) &&
                                                        (t = r.querySelector('input.form-control.pl-3[formcontrolname="amount"]')) &&
                                                        ((t.value = e.price), t.dispatchEvent(new Event("input", { bubbles: !0 })), t.dispatchEvent(new Event("change", { bubbles: !0 })), await new Promise((e) => setTimeout(e, 100)));
                                                }
                                            }));
                                    }
                                }
                                e++, v.updateProgress(i, 10 + e * r);
                            } catch (e) {}
                        v.updateProgress(i, 100), await t(async () => {});
                    } catch (e) {
                    } finally {
                        setTimeout(() => {
                            i && i.parentNode && i.remove();
                        }, 200);
                    }
                }
            },
        },
        o = {
            getSnapProfileDivHTML() {
                return `
                <div id="snap-profile-div" class="toolbar-container">
                    <div class="colors-tab-container">
                        <div data-tab="color-pricing" class="tab tab-color-pricing" style="background-color: #470CED; color: white;">
                            <img src="${chrome.runtime.getURL("assets/color-pricing-profile-ic.svg")}" class="white-icon" alt="Color & Pricing" />
                            <span>Color & Pricing Profiles</span>
                        </div>
                        <div data-tab="quick-color" class="tab tab-quick-color" style="background-color: white; color: #470CED;">
                            <img src="${chrome.runtime.getURL("assets/quick-color-selection-ic.svg")}" alt="Quick Color" />
                            <span>Quick Color Selection</span>
                        </div>
                    </div>

                    <div id="color-profiles-container" class="profile-actions ${"quick-color" === P.activeTab ? "hidden" : ""}">
                        ${this.getProfileDropdownHTML()}
                        ${this.getActionButtonsHTML()}
                    </div>

                    <div id="quick-tabs-container" class="quick-color-tabs ${"color-pricing" === P.activeTab ? "hidden" : ""}">
                        ${this.getQuickColorTabsHTML()}
                    </div>

                    <div class="execute-action-container ${"quick-color" === P.activeTab ? "hidden" : ""}">
                        <button id="executeActionButton" class="execute-button action-cta disabled" style="width: 100%; height: 36px;">
                            <img src="${chrome.runtime.getURL("assets/apply.svg")}" alt="Execute" style="filter: brightness(0) opacity(0.5);" />
                            <span>Execute Action</span>
                        </button>
                    </div>

                    <div class="apply-colors-container ${"color-pricing" === P.activeTab ? "hidden" : ""}">
                        <button id="applyColorsButton" class="apply-colors-button disabled" style="width: 100%; height: 36px;">
                            <img src="${chrome.runtime.getURL("assets/apply.svg")}" alt="Apply" style="filter: brightness(0) opacity(0.5);" />
                            <span>Apply Colors</span>
                        </button>
                    </div>
                </div>
                ${this.getCreateProfilePopupHTML()}
            `;
            },
            getProfileDropdownHTML() {
                return `
                <div class="select-dropdown snap-profile-dropdown" id="profileDropdown">
                    <div class="dropdown-header">
                        <span id="selectedValue">Select a profile</span>
                        <img src="${chrome.runtime.getURL("assets/dropdown-ic.svg")}" alt="Dropdown" />
                    </div>
                    <div id="dropdownMenu" class="dropdown-menu hidden">
                        <div id="profilesListState" class="profiles-list-state hidden">
                            <div class="search-container">
                                <div class="search-icon">
                                    <svg class="search-icon-svg" viewBox="0 0 24 24">
                                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5s2.91 6.5 6.5 6.5c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99 1.49-1.49-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5s4.5 2.01 4.5 4.5S11.99 14 9.5 14z"/>
                                    </svg>
                                </div>
                                <input id="searchInput" class="search-input" type="text" placeholder="Search profile.." />
                            </div>
                            <div id="dropdownList" class="dropdown-list"></div>
                        </div>

                        <div id="emptyState" class="empty-state hidden">
                            <div class="empty-state-content">
                                <div class="empty-state-image">
                                    <img src="${chrome.runtime.getURL("assets/no-profile-img.svg")}" alt="No profiles" />
                                </div>
                                <div class="empty-state-text">
                                    <p class="empty-state-title">You don't have profiles, yet.</p>
                                    <p class="empty-state-subtitle">Create or import a profile</p>
                                </div>
                                <div class="empty-state-actions">
                                    <button class="create-profile-button">
                                        <img src="${chrome.runtime.getURL("assets/add-row-ic.svg")}" class="white-icon" alt="Create" />
                                        <span>Create Profile</span>
                                    </button>
                                    <div class="custom-dotted action-button" data-tooltip="Import Profile">
                                        <img src="${chrome.runtime.getURL("assets/import-profile-ic.svg")}" alt="Import" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            },
            getEmptyStateHTML() {
                return `
                <div class="empty-state-content">
                    <div class="empty-state-image">
                        <img src="${chrome.runtime.getURL("assets/no-profile-img.svg")}" alt="No profiles" />
                    </div>
                    <div class="empty-state-text">
                        <p class="empty-state-title">You don't have profiles, yet.</p>
                        <p class="empty-state-subtitle">Create or import a profile</p>
                    </div>
                    <div class="empty-state-actions">
                        <button class="create-profile-button">
                            <img src="${chrome.runtime.getURL("assets/add-row-ic.svg")}" class="white-icon" alt="Create" />
                            <span>Create Profile</span>
                        </button>
                        <div class="custom-dotted action-button" data-tooltip="Import Profile">
                            <img src="${chrome.runtime.getURL("assets/import-profile-ic.svg")}" alt="Import" />
                        </div>
                    </div>
                </div>
            `;
            },
            getActionButtonsHTML() {
                return `
                <div class="action-buttons">
                    <div class="custom-dotted" data-tooltip="Create Profile">
                        <img src="${chrome.runtime.getURL("assets/add-row-ic.svg")}" alt="Add" />
                    </div>
                    <div class="custom-dotted" data-tooltip="Import Profile">
                        <img src="${chrome.runtime.getURL("assets/import-profile-ic.svg")}" alt="Import" />
                    </div>
                    <div id="export-profile-btn" class="custom-dotted disabled" data-tooltip="Export Profile">
                        <img src="${chrome.runtime.getURL("assets/export-profile-ic.svg")}" alt="Export" />
                    </div>
                    <div id="deleteIcon" class="custom-dotted disabled" data-tooltip="Delete Profile">
                        <img src="${chrome.runtime.getURL("assets/clear.svg")}" alt="Delete" />
                    </div>
                </div>
            `;
            },
            getQuickColorTabsHTML() {
                return `
                <div class="mini-tabs">
                    <div id="dark-colors-tab" class="mini-tab active">
                        <img src="${chrome.runtime.getURL("assets/quick-color-selection-ic.svg")}" class="blue-icon" alt="Quick Color">
                        <span>Dark Colors</span>
                    </div>
                    <div id="light-colors-tab" class="mini-tab">
                        <img src="${chrome.runtime.getURL("assets/quick-color-selection-ic.svg")}" class="gray-icon" alt="Quick Color">
                        <span>Light Colors</span>
                    </div>
                    <div id="all-colors-tab" class="mini-tab">
                        <img src="${chrome.runtime.getURL("assets/quick-color-selection-ic.svg")}" class="gray-icon" alt="Quick Color">
                        <span>All Colors</span>
                    </div>
                </div>
            `;
            },
            getCreateProfilePopupHTML() {
                return `
                <div class="popup-overlay" id="createProfilePopup">
                    <div class="popup-card">
                        <img src="${chrome.runtime.getURL("assets/close-popup-ic.svg")}" class="popup-close" alt="Close">
                        <img src="${chrome.runtime.getURL("assets/new-profile-img.svg")}" alt="New Profile" style="width: 52.28px; height: 57.85px;">
                        <h3 class="popup-title">Create New Profile</h3>
                        <div class="profile-input-container">
                            <input type="text" id="profileNameInput" placeholder="Enter a unique profile name.">
                        </div>
                        <div class="error-message">This profile name already exists.</div>
                        <button id="createProfileButton" class="create-button">
                            <img src="${chrome.runtime.getURL("assets/apply.svg")}" alt="Create" style="width: 16px; height: 16px;">
                            <span>Generate and Save</span>
                        </button>
                    </div>
                </div>
            `;
            },
        };
    function a() {
        var e = document.createElement("style");
        (e.textContent = `

            .toolbar-container {
                display: flex;
                align-items: center;
                background-color: #FAFAFA;
                width: 100%;
                height: 80px;
                border-radius: 8px;
                padding: 0 24px;
                gap: 20px;
                box-sizing: border-box;
            }

            .colors-tab-container {
                width: 372px;
                display: flex;
                align-items: center;
                flex-shrink: 0;
            }

            .color-profiles-container {
                width: 388px;
                display: flex;
                align-items: center;
                flex-direction: row;
                margin: 0;
                padding: 0;
                gap: 10px;
                flex-shrink: 0;
            }

            .execute-action-container,
            .apply-colors-container {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin: 0;
                padding: 0;
            }

            .tab {
                display: flex;
                align-items: center;
                cursor: pointer;
                border: 1.5px solid #470CED;
                font-size: 12px;
                font-weight: bold;
                height: 40px;
                padding-left: 16px;
                transition: all 0.2s ease;
            }

            .tab img {
                width: 20px;
                height: 20px;
                margin-right: 8px;
                transition: filter 0.2s ease;
            }

            .tab-color-pricing {
                width: 192px;
                border-radius: 4px 0 0 4px;
            }

            .tab-quick-color {
                width: 180px;
                border-radius: 0 4px 4px 0;
                padding-right: 8px;
                padding-left: 16px;
            }

            .profile-actions {
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 0;
                padding: 0;
            }

            .snap-profile-dropdown {
                position: relative;
                min-width: 216px;
                width: 216px;
                cursor: pointer;
                user-select: none;
            }

            .snap-profile-dropdown .dropdown-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 0;
                height: 40px;
                border: 1.5px solid #DCE0E5;
                border-radius: 4px;
                background: white;
                box-sizing: border-box;
                transition: border-color 0.2s ease;
            }

            .snap-profile-dropdown.focused .dropdown-header {
                border-color: #470CED;
            }

            .snap-profile-dropdown .dropdown-header span {
                padding-left: 12px;
            }

            .snap-profile-dropdown .dropdown-header img {
                margin-right: 12px;
            }

            .snap-profile-dropdown .dropdown-menu {
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: white;
                border: 1px solid #DCE0E5;
                border-radius: 4px;
                margin-top: 4px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            .snap-profile-dropdown .dropdown-menu:not(.hidden) {
                display: block;
            }

            .snap-profile-dropdown .dropdown-list {
                max-height: 200px;
                overflow-y: auto;
            }

            .snap-profile-dropdown .dropdown-item {
                padding: 8px 12px;
                cursor: pointer;
            }

            .snap-profile-dropdown .dropdown-item:hover {
                background: #F3F4F6;
            }

            .search-container {
                display: flex;
                align-items: center;
                padding: 8px 12px;
                border-bottom: 1px solid #E9EBEF;
            }

            .search-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                margin-right: 8px;
            }

            .search-icon-svg {
                width: 16px;
                height: 16px;
                fill: #9CA3AF;
            }

            .snap-profile-dropdown .search-input {
                width: 100%;
                border: none;
                outline: none;
                font-size: 12px;
                color: #1F2937;
                padding: 0;  
                background: transparent;
            }

            .snap-profile-dropdown .search-input::placeholder {
                color: #9CA3AF;
            }

            .action-buttons {
                display: flex;
                align-items: center;
                gap: 10px;
                margin: 0;
                padding: 0;
            }

            .custom-dotted {
                width: 32px;
                height: 32px;
                border-radius: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                cursor: pointer;
                background-color: transparent;
                border: none;
                background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23470CED' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
                background-position: center;
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }

            .custom-dotted[data-tooltip] {
                position: relative;
            }

            .custom-dotted[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
            }

            .custom-dotted[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
            }

            .custom-dotted[data-tooltip]:hover:before,
            .custom-dotted[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .custom-dotted img {
                width: 16px !important;
                height: 16px !important;
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
                pointer-events: none;
                filter: none;
            }

            .custom-dotted.disabled {
                background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23DCE0E5' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
                cursor: not-allowed;
            }

            .custom-dotted.disabled img {
                filter: brightness(0);
                opacity: 0.2 !important;
            }

            .custom-dotted.disabled img path {
                fill: #DCE0E5;
            }

            .custom-dotted:not(.disabled):hover {
                background-image: none;
                background-color: #470CED;
            }

            .custom-dotted:not(.disabled):hover img {
                filter: brightness(0) invert(1);
            }

            #deleteIcon:not(.disabled) {
                background-image: url("data:image/svg+xml,%3Csvg width='34' height='34' viewBox='0 0 34 34' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='1' y='1' width='32' height='32' rx='16' stroke='%23FF391F' stroke-linecap='round' stroke-linejoin='round' stroke-dasharray='1 3'/%3E%3C/svg%3E");
            }

            #deleteIcon:not(.disabled) img {
                filter: none;
            }

            #deleteIcon:not(.disabled):hover {
                background-image: none;
                background-color: #FF391F;
            }

            #deleteIcon:not(.disabled):hover img {
                filter: brightness(0) invert(1);
            }

            .execute-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                background-color: #CFD4D4;
                color: rgba(0, 0, 0, 0.5);
                border-radius: 9999px;
                padding: 8px 16px;
                border: none;
                transition: background-color 0.2s ease;
                width: 100%;
                height: 36px;
            }

            .execute-button.disabled {
                cursor: not-allowed;
            }

            .execute-button.disabled img {
                filter: brightness(0) opacity(0.5);
            }

            .execute-button:not(.disabled) {
                background-color: #470CED;
                color: white;
                cursor: pointer;
            }

            .execute-button:not(.disabled):hover {
                background-color: #2A00A0;
            }

            .execute-button img {
                width: 20px;
                height: 20px;
            }

            .execute-button:not(.disabled) img {
                filter: brightness(0) invert(1) !important;
            }

            .apply-colors-button {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                background-color: #CFD4D4;
                color: rgba(0, 0, 0, 0.5);
                border-radius: 9999px;
                padding: 8px 16px;
                border: none;
                transition: background-color 0.2s ease;
                width: 100%;
                height: 36px;
            }

            .apply-colors-button.disabled {
                cursor: not-allowed;
            }

            .apply-colors-button.disabled img {
                filter: brightness(0) opacity(0.5);
            }

            .apply-colors-button:not(.disabled) {
                background-color: #470CED;
                color: white;
                cursor: pointer;
            }

            .apply-colors-button:not(.disabled):hover {
                background-color: #2A00A0;
            }

            .apply-colors-button img {
                width: 20px;
                height: 20px;
            }

            .apply-colors-button:not(.disabled) img {
                filter: brightness(0) invert(1) !important;
            }

            .mini-tabs {
                display: flex;
                width: 100%;
                height: 40px;
                box-shadow: 0 1px 3px rgba(96, 108, 128, 0.05);
            }

            .mini-tab {
                display: flex;
                align-items: center;
                padding: 0 10px;
                gap: 8px;
                cursor: pointer;
                background: white;
                transition: all 0.2s ease;
                width: 123px;
            }

            #dark-colors-tab {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border: 1.5px solid #E9EBEF;
                border-right: none;
            }

            #light-colors-tab {
                border: 1.5px solid #E9EBEF;
            }

            #all-colors-tab {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border: 1.5px solid #E9EBEF;
                border-left: none;
            }

            .white-icon {
                filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
            }

            .blue-icon {
                filter: brightness(0) saturate(100%) invert(23%) sepia(75%) saturate(6764%) hue-rotate(246deg) brightness(89%) contrast(101%);
            }

            .gray-icon {
                filter: brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%);
            }

            .disabled-icon {
                filter: brightness(0) saturate(100%) invert(91%) sepia(5%) saturate(188%) hue-rotate(185deg) brightness(93%) contrast(87%);
            }

            .popup-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .popup-card {
                width: 270px;
                background: white;
                border-radius: 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
                padding: 40px 20px 20px;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .popup-close {
                position: absolute;
                top: 20px;
                right: 20px;
                width: 24px;
                height: 24px;
                cursor: pointer;
            }

            .popup-title {
                color: #606F95;
                font-size: 12px;
                font-weight: bold;
                margin-top: 14px;
                margin-bottom: 14px;
            }

            .profile-input-container {
                border: 1.5px solid #DCE0E5;
                border-radius: 4px;
                height: 40px;
                width: 100%;
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 0;
                transition: border-color 0.2s ease;
            }

            .profile-input-container input {
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
                padding: 0 12px;
                font-size: 12px;
                background: transparent;
            }

            .profile-input-container:focus-within {
                border-color: #470CED;
            }

            .profile-input-container.error {
                border-color: #FF391F;
            }

            .error-message {
                color: #FF391F;
                font-size: 10px;
                margin-top: 8px;
                display: none;
                text-align: left;
                width: 100%;
            }

            .create-button {
                width: 100%;
                height: 36px;
                background: #CFD4D4;
                border: none;
                border-radius: 4px;
                color: rgba(0, 0, 0, 0.5);
                font-size: 12px;
                font-weight: 500;
                cursor: not-allowed;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                margin-top: 20px;
            }

            .create-button img {
                filter: brightness(0);
                opacity: 0.5;
            }

            .create-button.active {
                background: #470CED;
                color: white;
                cursor: pointer;
            }

            .create-button.active img {
                filter: brightness(0) invert(1);
                opacity: 1;
            }

            .create-button.active:hover {
                background-color: #2A00A0;
            }

            .empty-state-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 18px;
            }

            .empty-state-image {
                width: 50.78px;
                height: 57.85px;
            }

            .empty-state-text {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1.5px;
                text-align: center;
            }

            .empty-state-title {
                font-size: 12px;
                font-weight: bold;
                color: #607096;
                margin: 0;
            }

            .empty-state-subtitle {
                font-size: 12px;
                font-weight: normal;
                color: #607096;
                margin: 0;
            }

            .snap-profile-container .custom-dotted[data-tooltip] {
                position: relative;
            }

            .snap-profile-container .custom-dotted[data-tooltip]:before {
                content: attr(data-tooltip);
                position: absolute;
                bottom: calc(100% + 8px);
                left: 50%;
                transform: translateX(-50%);
                padding: 4px 12px;
                background-color: #1F2937;
                color: white;
                font-size: 12px;
                border-radius: 999px;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
            }

            .snap-profile-container .custom-dotted[data-tooltip]:after {
                content: '';
                position: absolute;
                bottom: calc(100% + 4px);
                left: 50%;
                transform: translateX(-50%);
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #1F2937;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.2s ease, visibility 0.2s ease;
                z-index: 1000;
            }

            .snap-profile-container .custom-dotted[data-tooltip]:hover:before,
            .snap-profile-container .custom-dotted[data-tooltip]:hover:after {
                opacity: 1;
                visibility: visible;
            }

            .hidden {
                display: none !important;
            }

            .dropdown-item {
                padding: 8px 12px;
                cursor: pointer;
            }

            .dropdown-item:hover {
                background-color: #F3F4F6;
            }

            .mini-tab.active {
                background-color: #E9EBF2;
            }

            .mini-tab.active span {
                color: #470CED;
                font-weight: 700;
            }

            .mini-tab:not(.active):hover {
                background-color: #F9FAFC;
            }

            .mini-tab:not(.active) span {
                color: #606F95;
                font-weight: 500;
            }

            .create-profile-button {
                display: flex;
                align-items: center;
                gap: 10px;
                background-color: #470CED;
                color: white;
                padding: 8px 16px;
                border-radius: 9999px;
                font-size: 12px;
                font-weight: 500;
                border: none;
                cursor: pointer;
                flex-grow: 1;
            }

            .create-profile-button:hover {
                background-color: #2A00A0;
            }

            .empty-state-actions {
                display: flex;
                align-items: center;
                gap: 10px;
                width: 100%;
            }

            .search-icon-svg {
                width: 16px;
                height: 16px;
                fill: #470CED;
            }

            .dropdown-list {
                max-height: 220px;
                overflow-y: auto;
            }

            .popup-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: none;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            }

            .select-dropdown span {
                padding-left: 12px;
            }

            .select-dropdown img {
                margin-right: 12px;
            }

            .mini-tab {
                display: flex;
                align-items: center;
                padding: 0 10px;
                gap: 8px;
                cursor: pointer;
                background: white;
                transition: all 0.2s ease;
                width: 123px;
            }

            .mini-tab img {
                width: 16px;
                height: 16px;
            }

            .mini-tab span {
                font-size: 12px;
                font-weight: 400;  
            }

            .mini-tab.active span {
                font-weight: 700;  
            }

            .snap-profile-dropdown .empty-state {
                padding: 40px 20px 20px 20px;
            }

            .snap-profile-dropdown .empty-state-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 16px;
            }

            .snap-profile-dropdown .empty-state-image {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                padding: 0;
            }

            .snap-profile-dropdown .empty-state-image img {
                display: block;
                margin: 0 auto;
                padding: 0;
            }

            .snap-profile-dropdown .empty-state-text {
                text-align: center;
            }

            .snap-profile-dropdown .empty-state-title {
                font-size: 12px;
                font-weight: 700;  
                color: #60709C;  
                margin: 0;
            }

            .snap-profile-dropdown .empty-state-subtitle {
                font-size: 12px;
                color: #60709C;
                margin: 0;
            }

            .snap-profile-dropdown .empty-state-actions {
                display: flex;
                align-items: center;
                gap: 10px;
                width: 100%;
            }

            .snap-profile-dropdown .create-profile-button {
                height: 32px;
                display: flex;
                align-items: center;
                gap: 4px;
                background-color: #470CED;
                padding: 0 16px;
                border-radius: 9999px;
                border: none;
                cursor: pointer;
                flex-grow: 1;
                justify-content: flex-start;
            }

            .snap-profile-dropdown .create-profile-button span {
                font-size: 12px;
                font-weight: 500;
                color: white;
                padding-left: 0;
                line-height: normal;  
                font-family: inherit;  
            }

            .snap-profile-dropdown .create-profile-button img {
                width: 16px;
                height: 16px;
                margin-right: 0;
            }

            #snap-profile-div {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 16px;
                background: white;
                border-radius: 8px;
                border: 1px solid #E9EBEF;
            }

            .snap-profile-dropdown #dropdownList .dropdown-item {
                padding: 8px 12px;
                cursor: pointer;
                font-size: 12px;
                font-weight: 400;
                color: #1F2937;
                display: block;
                width: 100%;
                box-sizing: border-box;
                text-align: left;
                background: none;
                border: none;
                line-height: normal;
                transition: all 0.2s ease;
            }

            .snap-profile-dropdown #dropdownList .dropdown-item:hover {
                background: #F3F4F6;
            }

            .snap-profile-dropdown #dropdownList .dropdown-item.selected {
                font-weight: 700;
                color: #470CED;
            }

            .snap-profile-dropdown #dropdownList .dropdown-item.selected:hover {
                background: #F3F4F6;
            }

            .empty-state-actions .custom-dotted {
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .empty-state-actions .custom-dotted img {
                margin: 0;
            }
        `),
            document.head.appendChild(e);
    }
    const s = {
            productcolorProfiles: {
                colors: [
                    { name: "Asphalt", class: "checkbox-asphalt", category: "Dark Colors" },
                    { name: "Baby Blue", class: "checkbox-baby_blue", category: "Light Colors" },
                    { name: "Black", class: "checkbox-black", category: "Dark Colors" },
                    { name: "Brown", class: "checkbox-brown", category: "Dark Colors" },
                    { name: "Cranberry", class: "checkbox-cranberry", category: "Dark Colors" },
                    { name: "Dark Heather", class: "checkbox-dark_heather", category: "Dark Colors" },
                    { name: "Grass", class: "checkbox-grass", category: "Dark Colors" },
                    { name: "Heather Blue", class: "checkbox-heather_blue", category: "Dark Colors" },
                    { name: "Heather Grey", class: "checkbox-heather_grey", category: "Light Colors" },
                    { name: "Kelly Green", class: "checkbox-kelly_green", category: "Dark Colors" },
                    { name: "Lemon", class: "checkbox-lemon", category: "Light Colors" },
                    { name: "Navy", class: "checkbox-navy", category: "Dark Colors" },
                    { name: "Olive", class: "checkbox-olive", category: "Dark Colors" },
                    { name: "Orange", class: "checkbox-orange", category: "Neutral Colors" },
                    { name: "Pink", class: "checkbox-pink", category: "Light Colors" },
                    { name: "Purple", class: "checkbox-purple", category: "Dark Colors" },
                    { name: "Red", class: "checkbox-red", category: "Dark Colors" },
                    { name: "Royal", class: "checkbox-royal", category: "Dark Colors" },
                    { name: "Silver", class: "checkbox-silver", category: "Light Colors" },
                    { name: "Slate", class: "checkbox-slate", category: "Neutral Colors" },
                    { name: "White", class: "checkbox-white", category: "Light Colors" },
                    { name: "Dark Green", class: "checkbox-dark_green", category: "Dark Colors" },
                    { name: "Burgundy", class: "checkbox-burgundy", category: "Dark Colors" },
                    { name: "Golden Yellow", class: "checkbox-golden_yellow", category: "Light Colors" },
                    { name: "Purple Heather", class: "checkbox-purple_heather", category: "Dark Colors" },
                    { name: "Red Heather", class: "checkbox-red_heather", category: "Dark Colors" },
                    { name: "Olive Heather", class: "checkbox-olive_heather", category: "Dark Colors" },
                    { name: "Pink Heather", class: "checkbox-pink_heather", category: "Light Colors" },
                    { name: "Sapphire", class: "checkbox-sapphire", category: "Dark Colors" },
                    { name: "Forest Green", class: "checkbox-forest_green", category: "Dark Colors" },
                    { name: "Neon Pink", class: "checkbox-neon_pink", category: "Light Colors" },
                    { name: "Black Athletic Heather", class: "checkbox-black_athletic_heather", category: "Neutral Colors" },
                    { name: "Black White", class: "checkbox-black_white", category: "Light Colors" },
                    { name: "Dark Heather White", class: "checkbox-dark_heather_white", category: "Light Colors" },
                    { name: "Navy Athletic Heather", class: "checkbox-navy_athletic_heather", category: "Neutral Colors" },
                    { name: "Navy White", class: "checkbox-navy_white", category: "Light Colors" },
                    { name: "Red White", class: "checkbox-red_white", category: "Light Colors" },
                    { name: "Royal Blue White", class: "checkbox-royal_blue_white", category: "Light Colors" },
                    { name: "Sage Green", class: "checkbox-sage_green", category: "Dark Colors" },
                    { name: "Bright Pink", class: "checkbox-bright_pink", category: "Light Colors" },
                    { name: "Dusty Blue", class: "checkbox-dusty_blue", category: "Light Colors" },
                    { name: "Brushed Steel", class: "checkbox-brushed_steel", category: "Neutral Colors" },
                ],
            },
            findColorByClass(t) {
                return this.productcolorProfiles.colors.find((e) => e.class === t);
            },
            shouldSelectColor(e, t) {
                switch (t) {
                    case "dark":
                        return "Dark Colors" === e || "Neutral Colors" === e;
                    case "light":
                        return "Light Colors" === e || "Neutral Colors" === e;
                    case "all":
                        return !0;
                    default:
                        return !1;
                }
            },
            handleColorSelection(e, l) {
                e = e.querySelectorAll(".color-checkbox-container");
                const a = new Map();
                e.forEach((e) => {
                    var t,
                        r = e.querySelector("colorcheckbox");
                    if (r) {
                        const o = Array.from(r.classList)
                            .find((e) => e.endsWith("-checkbox"))
                            ?.replace("-checkbox", "");
                        o &&
                            (r = e.querySelector('span[class*="color-checkbox"]')) &&
                            (e = this.productcolorProfiles.colors.find((e) => e.class === "checkbox-" + o || e.name.toLowerCase().replace(/\s+/g, "_") === o)) &&
                            ((t = null !== r.querySelector(".sci-icon.sci-check.checkmark")), a.set(e.name, { container: r, info: e, currentlySelected: t }));
                    }
                }),
                    a.forEach(({ container: e, info: t, currentlySelected: r }, o) => {
                        var a = "Neutral Colors" === t.category,
                            s = "Dark Colors" === t.category,
                            c = "Light Colors" === t.category;
                        let i = r;
                        switch (l) {
                            case "dark":
                                s || a ? (i = !0) : c && (i = !1);
                                break;
                            case "light":
                                c || a ? (i = !0) : s && (i = !1);
                                break;
                            case "all":
                                i = !0;
                                break;
                            case "clear":
                                i = !1;
                        }
                        r !== i && e.click();
                    });
            },
            handleButtonClick(e, t) {
                var r = document.querySelector(".color-group-container.consolidated-dimension-container.pt-base.p-small.d-block");
                r && this.handleColorSelection(r, t);
            },
            createMiniTabs() {
                const a = document.createElement("div"),
                    s =
                        ((a.className = "product-color-buttons"),
                        (a.style.width = "100%"),
                        (a.style.display = "flex"),
                        [
                            { id: "copy-colors", text: "Copy", mode: "copy", icon: "copy-ic.svg" },
                            { id: "paste-colors", text: "Paste", mode: "paste", icon: "paste-ic.svg" },
                            { id: "dark-colors", text: "Dark", mode: "dark", icon: "quick-color-selection-ic.svg" },
                            { id: "light-colors", text: "Light", mode: "light", icon: "quick-color-selection-ic.svg" },
                            { id: "all-colors", text: "All", mode: "all", icon: "quick-color-selection-ic.svg" },
                            { id: "clear-colors", text: "Clear", mode: "clear", icon: "clear-colors-ic.svg" },
                        ]);
                return (
                    s.forEach((e, t) => {
                        const r = document.createElement("button");
                        (r.id = `product-${e.id}-btn`),
                            (r.className = "color-action-button"),
                            (r.style.cssText = `
                    flex: 1;
                    height: 40px;
                    border: 1.5px solid #E9EBEF;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    padding: 0 10px;
                    outline: none;
                `),
                            r.addEventListener("mouseenter", () => {
                                r.classList.contains("selected") || (r.style.backgroundColor = "#F9FAFC");
                            }),
                            r.addEventListener("mouseleave", () => {
                                r.classList.contains("selected") || (r.style.backgroundColor = "white");
                            }),
                            r.addEventListener("click", () => {
                                "copy" === e.mode ? this.copySelectedColors() : "paste" === e.mode ? this.pasteSelectedColors() : this.handleColorSelection(document.querySelector(".color-group-container"), e.mode);
                            }),
                            0 === t
                                ? ((r.style.borderTopLeftRadius = "4px"), (r.style.borderBottomLeftRadius = "4px"), (r.style.borderRight = "none"))
                                : t === s.length - 1
                                ? ((r.style.borderTopRightRadius = "4px"), (r.style.borderBottomRightRadius = "4px"), (r.style.borderLeft = "none"))
                                : ((r.style.borderLeft = "none"), (r.style.borderRight = "none"));
                        var t = document.createElement("img"),
                            o =
                                ((t.src = chrome.runtime.getURL("assets/" + e.icon)),
                                (t.style.filter =
                                    "clear" === e.mode
                                        ? "brightness(0) saturate(100%) invert(32%) sepia(85%) saturate(2726%) hue-rotate(343deg) brightness(99%) contrast(100%)"
                                        : "brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%)"),
                                (t.style.width = "16px"),
                                (t.style.height = "16px"),
                                document.createElement("span"));
                        (o.textContent = e.text),
                            (o.style.cssText = `
                    font-size: 12px;
                    color: ${"clear" === e.mode ? "#FA583A" : "#606F95"};
                    font-weight: 400;
                    transition: all 0.2s ease;
                `),
                            r.appendChild(t),
                            r.appendChild(o),
                            a.appendChild(r);
                    }),
                    a
                );
            },
            createContainer() {
                var e = document.createElement("div"),
                    t =
                        ((e.className = "product-color-actions"),
                        (e.style.cssText = `
                width: 100%;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin-top: 0px;
                margin-bottom: 10px;
                padding: 0 10px;
                box-sizing: border-box;
            `),
                        this.createMiniTabs());
                return e.appendChild(t), e;
            },
            injectContainer(e) {
                var t;
                e.querySelector(".product-color-actions") || ((t = this.createContainer()), e.appendChild(t));
            },
            init() {
                new MutationObserver((e) => {
                    e.forEach((e) => {
                        "childList" === e.type &&
                            document.querySelectorAll(".color-groups-container").forEach((e) => {
                                null !== e.offsetParent && this.injectContainer(e);
                            });
                    });
                }).observe(document.body, { childList: !0, subtree: !0 }),
                    document.querySelectorAll(".color-groups-container").forEach((e) => {
                        null !== e.offsetParent && this.injectContainer(e);
                    });
            },
            colorMappings: null,
            mapColorGroup(e) {
                const a = { dark: new Set(), light: new Set(), all: new Set(), neutral: new Set(), checkboxes: new Map(), colorGroup: e };
                return (
                    e.querySelectorAll(".color-checkbox-container").forEach((e) => {
                        var t,
                            r = e.querySelector("colorcheckbox");
                        if (r) {
                            const o = Array.from(r.classList)
                                .find((e) => e.endsWith("-checkbox"))
                                ?.replace("-checkbox", "");
                            o &&
                                (r = e.querySelector('input[type="checkbox"]')) &&
                                (e = this.productcolorProfiles.colors.find((e) => e.class === "checkbox-" + o || e.name.toLowerCase().replace(/\s+/g, "_") === o)) &&
                                ((t = e.name.toLowerCase().replace(/\s+/g, "_")),
                                a.checkboxes.set(t, { checkbox: r, info: e }),
                                a.all.add(t),
                                "Neutral Colors" === e.category ? (a.neutral.add(t), a.dark.add(t), a.light.add(t)) : "Dark Colors" === e.category ? a.dark.add(t) : "Light Colors" === e.category && a.light.add(t));
                        }
                    }),
                    console.log("", {}),
                    a
                );
            },
            async copySelectedColors() {
                var e = document.querySelector(".color-group-container");
                if (e) {
                    const t = [];
                    if (
                        (e.querySelectorAll(".color-checkbox-container").forEach((e) => {
                            if (e.querySelector(".sci-icon.sci-check.checkmark")) {
                                e = e.querySelector('span[class*="color-checkbox"]');
                                if (e) {
                                    var colorClass = Array.from(e.classList).find((e) => e.startsWith("checkbox-"));
                                    if (colorClass) {
                                        var colorName = colorClass.replace("checkbox-", "");
                                        if (colorName === "forest_green") {
                                            colorName = "dark_green";
                                        }
                                        t.push(colorName);
                                    }
                                }
                            }
                        }),
                        0 < t.length)
                    )
                        try {
                            await chrome.storage.local.set({ "snap-copied-colors": t });
                        } catch (e) {}
                }
            },
            async pasteSelectedColors() {
                try {
                    var e = (await chrome.storage.local.get(["snap-copied-colors"]))["snap-copied-colors"];
                    if (e && 0 !== e.length) {
                        var t = document.querySelector(".color-group-container");
                        if (t) {
                            var r = t.querySelectorAll(".color-checkbox-container .color-checkbox");
                            const o = new Set();
                            
                            r.forEach((e) => {
                                e = Array.from(e.classList).find((e) => e.startsWith("checkbox-"));
                                e && o.add(e.replace("checkbox-", ""));
                            });
                            
                            const a = e.map(color => {
                                if (color === "dark_green" && !o.has("dark_green") && o.has("forest_green")) {
                                    return "forest_green";
                                }
                                return color;
                            }).filter((e) => o.has(e));

                            0 !== a.length &&
                                (r.forEach((e) => {
                                    e.querySelector(".sci-icon.sci-check.checkmark") && e.click();
                                }),
                                r.forEach((e) => {
                                    var t = Array.from(e.classList).find((e) => e.startsWith("checkbox-"));
                                    t && ((t = t.replace("checkbox-", "")), a.includes(t)) && !e.querySelector(".sci-icon.sci-check.checkmark") && e.click();
                                }));
                        }
                    }
                } catch (e) {}
            },
        },
        c = {
            lastClickedProductId: null,
            state: { isApplyingPrices: !1 },
            setupEditButtonTracking() {
                document.addEventListener("click", (e) => {
                    const t = e.target.closest("button");
                    t &&
                        (e = Object.keys(y.products).find((e) => {
                            e = y.products[e];
                            return t.classList.contains(e.editButtonClass);
                        })) &&
                        (this.lastClickedProductId = e);
                });
            },
            identifyCurrentProduct() {
                var e = {
                    STANDARD_TSHIRT: "Standard T-shirt",
                    PREMIUM_TSHIRT: "Premium T-shirt",
                    VNECK: "V-neck T-shirt",
                    TANK_TOP: "Tank top",
                    STANDARD_LONG_SLEEVE: "Long sleeve T-shirt",
                    RAGLAN: "Raglan",
                    STANDARD_SWEATSHIRT: "Sweatshirt",
                    STANDARD_PULLOVER_HOODIE: "Pullover hoodie",
                    ZIP_HOODIE: "Zip hoodie",
                    POP_SOCKET: "PopSockets",
                    PHONE_CASE_APPLE_IPHONE: "iPhone cases",
                    THROW_PILLOW: "Throw pillows",
                    TOTE_BAG: "Tote bag",
                    TUMBLER: "Tumbler",
                };
                if (this.lastClickedProductId) {
                    var t = e[this.lastClickedProductId.split("-")[0]];
                    if (t) return t;
                }
                var r = window.location.href;
                for (const i of [/[?&]product=([^&]+)/, /[?&]type=([^&]+)/, /[?&]productor-product-type=([^&]+)/]) {
                    var o = r.match(i);
                    if (o) {
                        var a,
                            s,
                            c = decodeURIComponent(o[1]).toUpperCase();
                        if (e[c]) return e[c];
                        for ([a, s] of Object.entries(e)) if (c.includes(a)) return s;
                    }
                }
                return null;
            },
            init() {
                this.setupEditButtonTracking(),
                    new MutationObserver((e) => {
                        e.forEach((e) => {
                            "childList" === e.type &&
                                document.querySelectorAll(".nav-container").forEach((e) => {
                                    null !== e.offsetParent && this.injectContainer(e);
                                });
                        });
                    }).observe(document.body, { childList: !0, subtree: !0 }),
                    document.querySelectorAll(".nav-container").forEach((e) => {
                        null !== e.offsetParent && this.injectContainer(e);
                    });
            },
            priceProfiles: {
                products: {
                    "Standard T-shirt": {
                        marketplaces: {
                            ".com": { defaultPrices: 19.99, suggestedPrices: 19.99, marketAverage: 17.99, maxPrices: 34.99 },
                            ".co.uk": { defaultPrices: 17.49, suggestedPrices: 16.99, marketAverage: 15.99, maxPrices: 17.49 },
                            ".de": { defaultPrices: 17.49, suggestedPrices: 17.49, marketAverage: 16.99, maxPrices: 17.99 },
                            ".fr": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 19.49 },
                            ".it": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 19.49 },
                            ".es": { defaultPrices: 16.49, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 19.49 },
                            ".co.jp": { defaultPrices: 2e3, suggestedPrices: 2299, marketAverage: 2199, maxPrices: 6499 },
                        },
                    },
                    "Premium T-shirt": { marketplaces: { ".com": { defaultPrices: 19.99, suggestedPrices: 21.99, marketAverage: 19.99, maxPrices: 44.99 } } },
                    "V-neck T-shirt": {
                        marketplaces: {
                            ".com": { defaultPrices: 19.99, suggestedPrices: 21.99, marketAverage: 20.99, maxPrices: 34.99 },
                            ".co.uk": { defaultPrices: 15.99, suggestedPrices: 17.99, marketAverage: 16.99, maxPrices: 34.99 },
                            ".de": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 34.99 },
                            ".fr": { defaultPrices: 17.99, suggestedPrices: 19.99, marketAverage: 18.99, maxPrices: 34.99 },
                            ".it": { defaultPrices: 17.99, suggestedPrices: 19.99, marketAverage: 18.99, maxPrices: 34.99 },
                            ".es": { defaultPrices: 17.49, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 34.99 },
                        },
                    },
                    "Tank top": {
                        marketplaces: {
                            ".com": { defaultPrices: 19.99, suggestedPrices: 21.99, marketAverage: 19.99, maxPrices: 34.99 },
                            ".co.uk": { defaultPrices: 16.69, suggestedPrices: 17.99, marketAverage: 16.99, maxPrices: 29.99 },
                            ".de": { defaultPrices: 17.99, suggestedPrices: 19.99, marketAverage: 18.99, maxPrices: 29.99 },
                            ".fr": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 29.99 },
                            ".it": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 29.99 },
                            ".es": { defaultPrices: 16.99, suggestedPrices: 18.99, marketAverage: 17.99, maxPrices: 29.99 },
                        },
                    },
                    "Long sleeve T-shirt": {
                        marketplaces: {
                            ".com": { defaultPrices: 22.99, suggestedPrices: 23.99, marketAverage: 21.99, maxPrices: 39.99 },
                            ".co.uk": { defaultPrices: 21.99, suggestedPrices: 21.99, marketAverage: 19.99, maxPrices: 34.99 },
                            ".de": { defaultPrices: 22.99, suggestedPrices: 22.99, marketAverage: 21.99, maxPrices: 34.99 },
                            ".fr": { defaultPrices: 19.99, suggestedPrices: 20.99, marketAverage: 19.99, maxPrices: 39.99 },
                            ".it": { defaultPrices: 19.99, suggestedPrices: 20.99, marketAverage: 19.99, maxPrices: 39.99 },
                            ".es": { defaultPrices: 19.99, suggestedPrices: 20.99, marketAverage: 19.99, maxPrices: 39.99 },
                            ".co.jp": { defaultPrices: 2750, suggestedPrices: 2999, marketAverage: 2799, maxPrices: 6499 },
                        },
                    },
                    Raglan: {
                        marketplaces: {
                            ".com": { defaultPrices: 23.99, suggestedPrices: 24.99, marketAverage: 22.99, maxPrices: 44.99 },
                            ".co.uk": { defaultPrices: 17.99, suggestedPrices: 19.99, marketAverage: 18.99, maxPrices: 29.99 },
                            ".de": { defaultPrices: 19.99, suggestedPrices: 21.99, marketAverage: 19.99, maxPrices: 34.99 },
                            ".fr": { defaultPrices: 20.99, suggestedPrices: 21.99, marketAverage: 20.99, maxPrices: 34.99 },
                            ".it": { defaultPrices: 20.99, suggestedPrices: 21.99, marketAverage: 20.99, maxPrices: 34.99 },
                            ".es": { defaultPrices: 20.99, suggestedPrices: 21.99, marketAverage: 20.99, maxPrices: 34.99 },
                        },
                    },
                    Sweatshirt: {
                        marketplaces: {
                            ".com": { defaultPrices: 31.99, suggestedPrices: 32.99, marketAverage: 29.99, maxPrices: 33.99 },
                            ".co.uk": { defaultPrices: 31.99, suggestedPrices: 31.99, marketAverage: 29.99, maxPrices: 44.99 },
                            ".de": { defaultPrices: 33.99, suggestedPrices: 34.99, marketAverage: 31.99, maxPrices: 49.99 },
                            ".fr": { defaultPrices: 28.49, suggestedPrices: 29.99, marketAverage: 28.99, maxPrices: 49.99 },
                            ".it": { defaultPrices: 28.99, suggestedPrices: 29.99, marketAverage: 28.99, maxPrices: 49.99 },
                            ".es": { defaultPrices: 28.99, suggestedPrices: 29.99, marketAverage: 28.99, maxPrices: 49.99 },
                            ".co.jp": { defaultPrices: 3960, suggestedPrices: 3999, marketAverage: 3899, maxPrices: 7999 },
                        },
                    },
                    "Pullover hoodie": {
                        marketplaces: {
                            ".com": { defaultPrices: 31.99, suggestedPrices: 31.99, marketAverage: 29.99, maxPrices: 35.99 },
                            ".co.uk": { defaultPrices: 28.99, suggestedPrices: 28.99, marketAverage: 27.99, maxPrices: 28.99 },
                            ".de": { defaultPrices: 32.99, suggestedPrices: 32.99, marketAverage: 30.99, maxPrices: 32.99 },
                            ".fr": { defaultPrices: 28.99, suggestedPrices: 28.99, marketAverage: 27.99, maxPrices: 28.99 },
                            ".it": { defaultPrices: 28.99, suggestedPrices: 27.99, marketAverage: 27.99, maxPrices: 28.99 },
                            ".es": { defaultPrices: 31.49, suggestedPrices: 32.99, marketAverage: 30.99, maxPrices: 33.99 },
                            ".co.jp": { defaultPrices: 4400, suggestedPrices: 4499, marketAverage: 4299, maxPrices: 7999 },
                        },
                    },
                    "Zip hoodie": {
                        marketplaces: {
                            ".com": { defaultPrices: 33.99, suggestedPrices: 33.99, marketAverage: 31.99, maxPrices: 37.99 },
                            ".co.uk": { defaultPrices: 28.99, suggestedPrices: 29.99, marketAverage: 28.99, maxPrices: 44.99 },
                            ".de": { defaultPrices: 32.99, suggestedPrices: 32.99, marketAverage: 29.99, maxPrices: 49.99 },
                            ".fr": { defaultPrices: 31.49, suggestedPrices: 32.99, marketAverage: 29.99, maxPrices: 49.99 },
                            ".it": { defaultPrices: 31.99, suggestedPrices: 32.99, marketAverage: 29.99, maxPrices: 49.99 },
                            ".es": { defaultPrices: 31.49, suggestedPrices: 32.99, marketAverage: 29.99, maxPrices: 49.99 },
                            ".co.jp": { defaultPrices: 4600, suggestedPrices: 4699, marketAverage: 4399, maxPrices: 7999 },
                        },
                    },
                    PopSockets: {
                        marketplaces: {
                            ".com": { defaultPrices: 14.99, suggestedPrices: 12.99, marketAverage: 12.99, maxPrices: 19.99 },
                            ".co.uk": { defaultPrices: 11.99, suggestedPrices: 10.99, marketAverage: 10.99, maxPrices: 19.99 },
                            ".de": { defaultPrices: 12.99, suggestedPrices: 10.99, marketAverage: 10.99, maxPrices: 19.99 },
                            ".fr": { defaultPrices: 14.99, suggestedPrices: 12.99, marketAverage: 12.99, maxPrices: 19.99 },
                            ".it": { defaultPrices: 14.99, suggestedPrices: 12.99, marketAverage: 12.99, maxPrices: 19.99 },
                            ".es": { defaultPrices: 14.49, suggestedPrices: 12.99, marketAverage: 12.99, maxPrices: 19.99 },
                        },
                    },
                    "iPhone cases": {
                        marketplaces: {
                            ".com": { defaultPrices: 17.99, suggestedPrices: 19.99, marketAverage: 18.99, maxPrices: 29.99 },
                            ".co.uk": { defaultPrices: 15.99, suggestedPrices: 13.99, marketAverage: 13.99, maxPrices: 17.99 },
                            ".de": { defaultPrices: 16.99, suggestedPrices: 14.99, marketAverage: 14.99, maxPrices: 19.99 },
                            ".fr": { defaultPrices: 17.99, suggestedPrices: 15.99, marketAverage: 15.99, maxPrices: 19.99 },
                            ".it": { defaultPrices: 17.99, suggestedPrices: 15.99, marketAverage: 15.99, maxPrices: 19.99 },
                            ".es": { defaultPrices: 17.99, suggestedPrices: 15.99, marketAverage: 15.99, maxPrices: 19.99 },
                            ".co.jp": { defaultPrices: 2e3, suggestedPrices: 2499, marketAverage: 2499, maxPrices: 6499 },
                        },
                    },
                    "Tote bag": { marketplaces: { ".com": { defaultPrices: 18.99, suggestedPrices: 21.99, marketAverage: 19.99, maxPrices: 24.99 } } },
                    "Throw pillows": { marketplaces: { ".com": { defaultPrices: 19.99, suggestedPrices: 23.99, marketAverage: 21.99, maxPrices: 44.99 } } },
                    Tumbler: { marketplaces: { ".com": { defaultPrices: 21.99, suggestedPrices: 20.99, marketAverage: 20.99, maxPrices: 22.99 } } },
                },
            },
            mapConfigKeyToProfileKey(e) {
                return (
                    {
                        STANDARD_TSHIRT: "Standard T-shirt",
                        PREMIUM_TSHIRT: "Premium T-shirt",
                        VNECK: "V-neck T-shirt",
                        TANK_TOP: "Tank top",
                        STANDARD_LONG_SLEEVE: "Long sleeve T-shirt",
                        RAGLAN: "Raglan",
                        STANDARD_SWEATSHIRT: "Sweatshirt",
                        STANDARD_PULLOVER_HOODIE: "Pullover hoodie",
                        ZIP_HOODIE: "Zip hoodie",
                        POP_SOCKET: "PopSockets",
                        PHONE_CASE_APPLE_IPHONE: "iPhone cases",
                        TOTE_BAG: "Tote bag",
                        THROW_PILLOW: "Throw pillows",
                        TUMBLER: "Tumbler",
                    }[e] || e
                );
            },
            storeCurrentPrices(e) {
                e = e.querySelectorAll(".input-group.p-0.col-4 input");
                const o = new Map();
                return (
                    e.forEach((e) => {
                        var t = e.closest(".form-row.pl-3");
                        if (t) {
                            const r = t.textContent.trim();
                            t = Object.keys(this.priceProfiles.products["Standard T-shirt"].marketplaces).find((e) => r.includes(e));
                            t && o.set(t, e.value);
                        }
                    }),
                    o
                );
            },
            restorePrices(e, o) {
                e.querySelectorAll(".input-group.p-0.col-4 input").forEach((e) => {
                    var t = e.closest(".form-row.pl-3");
                    if (t) {
                        const r = t.textContent.trim();
                        t = Object.keys(this.priceProfiles.products["Standard T-shirt"].marketplaces).find((e) => r.includes(e));
                        t && o.has(t) && (e.value = o.get(t));
                    }
                });
            },
            async applyPrices(e, t, r, o) {
                if (!this.state.isApplyingPrices)
                    try {
                        this.state.isApplyingPrices = !0;
                        var a = document.querySelectorAll(".price-action-button");
                        if (
                            (a.forEach((e) => {
                                (e.style.opacity = "0.5"), (e.style.cursor = "not-allowed"), (e.disabled = !0);
                            }),
                            this.priceProfiles.products[t])
                        )
                            for (const d of e.querySelectorAll('input.form-control.pl-3[formcontrolname="amount"]')) {
                                var s = d.closest(".price-container");
                                if (s) {
                                    var c = s.querySelector("strong");
                                    if (c) {
                                        const p = c.textContent.trim();
                                        var i,
                                            l,
                                            n = [".com", ".co.uk", ".de", ".fr", ".it", ".es", ".co.jp"].find((e) => p.includes(e));
                                        n &&
                                            (i = this.priceProfiles.products[t]?.marketplaces[n]) &&
                                            "number" == typeof (l = i[r]) &&
                                            (console.log("", {}),
                                            (d.value = l),
                                            d.dispatchEvent(new Event("input", { bubbles: !0 })),
                                            d.dispatchEvent(new Event("change", { bubbles: !0 })),
                                            d.dispatchEvent(new Event("blur", { bubbles: !0 })),
                                            await new Promise((e) => setTimeout(e, 400)));
                                    }
                                }
                            }
                    } catch (e) {
                    } finally {
                        (this.state.isApplyingPrices = !1),
                            document.querySelectorAll(".price-action-button").forEach((e) => {
                                (e.style.opacity = ""), (e.style.cursor = ""), (e.disabled = !1);
                            });
                    }
            },
            handleButtonClick(e, t) {
                var r = this.identifyCurrentProduct();
                if (r) {
                    t = { default: "defaultPrices", suggested: "suggestedPrices", market: "marketAverage", max: "maxPrices" }[t];
                    if (t) {
                        let e;
                        if (this.lastClickedProductId) e = this.lastClickedProductId.split("-")[0];
                        else {
                            var o = window.location.href;
                            let t = null;
                            for (const i of [/[?&]product=([^&]+)/, /[?&]type=([^&]+)/, /[?&]productor-product-type=([^&]+)/]) {
                                var a = o.match(i);
                                if (a) {
                                    t = decodeURIComponent(a[1]).toUpperCase();
                                    break;
                                }
                            }
                            t && (s = Object.keys(y.products).find((e) => t.includes(e))) && (e = s);
                        }
                        if (e) {
                            var s = y.products[e];
                            if (s) {
                                var c = document.querySelector(".nav-container");
                                if (c)
                                    try {
                                        this.applyPrices(c, r, t, s);
                                    } catch (e) {}
                            }
                        }
                    }
                }
            },
            createMiniTabs() {
                const a = document.createElement("div"),
                    s =
                        ((a.className = "product-price-buttons"),
                        (a.style.width = "100%"),
                        (a.style.display = "flex"),
                        [
                            { id: "default-prices", text: "Default", mode: "default" },
                            { id: "suggested-prices", text: "Suggested", mode: "suggested" },
                            { id: "market-average", text: "Average", mode: "market" },
                            { id: "max-prices", text: "Max Prices", mode: "max" },
                        ]);
                return (
                    s.forEach((e, t) => {
                        const r = document.createElement("button");
                        (r.id = `product-${e.id}-btn`),
                            (r.className = "price-action-button"),
                            (r.style.cssText = `
                    flex: 1;
                    height: 40px;
                    border: 1.5px solid #E9EBEF;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    padding: 0 10px;
                    outline: none;
                `),
                            r.addEventListener("mouseenter", () => {
                                r.classList.contains("selected") || (r.style.backgroundColor = "#F9FAFC");
                            }),
                            r.addEventListener("mouseleave", () => {
                                r.classList.contains("selected") || (r.style.backgroundColor = "white");
                            }),
                            r.addEventListener("click", () => {
                                this.handleButtonClick(r, e.mode);
                            }),
                            0 === t
                                ? ((r.style.borderTopLeftRadius = "4px"), (r.style.borderBottomLeftRadius = "4px"), (r.style.borderRight = "none"))
                                : t === s.length - 1
                                ? ((r.style.borderTopRightRadius = "4px"), (r.style.borderBottomRightRadius = "4px"), (r.style.borderLeft = "none"))
                                : ((r.style.borderLeft = "none"), (r.style.borderRight = "none"));
                        var t = document.createElement("img"),
                            o =
                                ((t.src = chrome.runtime.getURL("assets/price-ic.svg")),
                                (t.style.filter = "brightness(0) saturate(100%) invert(45%) sepia(11%) saturate(1129%) hue-rotate(189deg) brightness(94%) contrast(87%)"),
                                (t.style.width = "16px"),
                                (t.style.height = "16px"),
                                document.createElement("span"));
                        (o.textContent = e.text),
                            (o.style.cssText = `
                    font-size: 12px;
                    color: #606F95;
                    font-weight: 400;
                    transition: all 0.2s ease;
                `),
                            r.appendChild(t),
                            r.appendChild(o),
                            a.appendChild(r);
                    }),
                    a
                );
            },
            createContainer() {
                var e = document.createElement("div"),
                    t =
                        ((e.className = "product-price-actions"),
                        (e.style.cssText = `
                width: 100%;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin: 15px 0;
                padding: 0 15px;
                box-sizing: border-box;
                background: white;
            `),
                        this.createMiniTabs());
                return e.appendChild(t), e;
            },
            injectContainer(e) {
                var t, r;
                e.querySelector(".product-price-actions") || ((t = this.createContainer()), (r = e.firstChild) && r.nextSibling ? e.insertBefore(t, r.nextSibling) : e.appendChild(t));
            },
        };
    function e() {
        const r = () => {
            var e,
                t = document.querySelector("product-config-editor");
            (t = t && t.querySelector(".form-row.mb-base.ng-star-inserted"))
                ? document.querySelector(".snap-profile-container") ||
                  (((e = document.createElement("div")).className = "snap-profile-container"),
                  (e.style.cssText = "width: 100%; margin-bottom: 16px;"),
                  (e.innerHTML = o.getSnapProfileDivHTML()),
                  t.parentNode.insertBefore(e, t),
                  a(),
                  v.init(),
                  s.init(),
                  c.init(),
                  b.loadProfiles())
                : setTimeout(r, 500);
        };
        r();
    }
    async function w() {
        var e = x[0],
            t = document.querySelector(`.${e}-edit-btn`);
        if (!t) return !1;
        let r = 0;
        for (; r < 3; ) {
            var o = document.querySelector(".nav-container");
            if (!(o && null !== o.offsetParent)) return !0;
            t.click(), t.blur(), await new Promise((e) => setTimeout(e, 500)), r++;
        }
        return !document.querySelector(".nav-container")?.offsetParent;
    }
    "loading" === document.readyState ? document.addEventListener("DOMContentLoaded", () => setTimeout(e, 100)) : setTimeout(e, 100);
    const C = {
        _translationCache: new Map(),
        _reverseLookupCache: new Map(),
        detectPageLanguage() {
            var e = document.querySelector("button.btn.btn-secondary.dropdown-toggle.btn-lop");
            if (e) {
                e = e.textContent.trim().match(/\s*([A-Z]{2})\s*$/);
                if (e) {
                    var e = e[1],
                        t = { EN: "en", DE: "de", FR: "fr", IT: "it", ES: "es", JP: "ja" };
                    if (t[e]) return t[e];
                }
            }
            t = window.location.hostname;
            return t.endsWith(".de") ? "de" : t.endsWith(".fr") ? "fr" : t.endsWith(".es") ? "es" : t.endsWith(".it") ? "it" : t.endsWith(".co.jp") ? "ja" : "en";
        },
        getTranslationMapping() {
            return {
                "All products": { de: "Alle Produkte", fr: "Tous les produits", it: "Tutti i prodotti", es: "Todos los productos", ja: "すべての商品" },
                "Standard t-shirt": { de: "Standard T-Shirt", fr: "T-shirt standard", it: "T-shirt standard", es: "Camiseta estándar", ja: "スタンダードTシャツ" },
                "Premium t-shirt": { de: "Premium T-Shirt", fr: "T-shirt haut de gamme", it: "T-shirt Premium", es: "Camiseta premium", ja: "プレミアムTシャツ" },
                "V-neck t-shirt": { de: "V-Ausschnitt T-Shirt", fr: "T-shirt col V", it: "T-shirt con scollo a V", es: "Camiseta con cuello en V", ja: "VネックTシャツ" },
                "Tank top": { de: "Tank Top", fr: "Débardeur", it: "Canotta", es: "Camiseta sin mangas", ja: "タンクトップ" },
                "Long sleeve t-shirt": { de: "Langarm T-Shirt", fr: "T-shirt à manches longues", it: "T-shirt manica lunga", es: "Camiseta de manga larga", ja: "長袖Tシャツ" },
                Raglan: { de: "Raglan", fr: "Manches raglan", it: "Raglan", es: "Raglán", ja: "ラグラン" },
                Sweatshirt: { de: "Sweatshirt", fr: "Sweat-shirt", it: "Felpa", es: "Sudadera", ja: "トレーナー" },
                "Pullover hoodie": { de: "Pullover Hoodie", fr: "Sweat à capuche", it: "Felpa con cappuccio", es: "Sudadera con capucha", ja: "プルオーバーパーカー" },
                "Zip hoodie": { de: "Zip hoodie", fr: "Sweat à capuche zippé", it: "Felpa con cappuccio e zip", es: "Sudadera con cremallera", ja: "ジップパーカー" },
                PopSockets: { de: "PopSockets", fr: "PopSockets", it: "PopSockets", es: "PopSockets", ja: "ポップソケッツ" },
                "iPhone cases": { de: "iPhone-Hülle", fr: "Étuis iPhone", it: "Custodie iPhone", es: "Fundas para iPhone", ja: "iPhone用ケース" },
                "Tote bag": { de: "Tragetasche", fr: "Sac en toile", it: "Borsa shopper", es: "Bolso tote", ja: "トートバッグ" },
                "Throw pillows": { de: "Überwurfkissen", fr: "Coussin décoratif", it: "Cuscino decorativo", es: "Almohada", ja: "クッション" },
                Tumbler: { de: "Tumbler", fr: "Tumbler", it: "Bicchiere Tumbler", es: "Tumbler", ja: "タンブラー" },
            };
        },
        initializeCaches() {
            var e,
                t,
                r,
                o,
                a = this.detectPageLanguage();
            this._translationCache.clear(), this._reverseLookupCache.clear();
            for ([e, t] of Object.entries(this.getTranslationMapping())) {
                var s = t[a] || e;
                this._translationCache.set(s, e), this._translationCache.set(s.toLowerCase(), e), "ja" === a && this._translationCache.set(s.replace(/\s+/g, ""), e);
            }
            for ([r, o] of Object.entries(this.getTranslationMapping())) {
                var c = o[a] || r;
                this._reverseLookupCache.set(r, c), this._reverseLookupCache.set(r.toLowerCase(), c), "ja" === a && this._reverseLookupCache.set(r.replace(/\s+/g, ""), c);
            }
            console.log("", {});
        },
        getEnglishName(e) {
            if (!e) return null;
            this.detectPageLanguage();
            0 === this._translationCache.size && this.initializeCaches();
            var t = [e, e.toLowerCase(), e.replace(/\s+/g, ""), e.toLowerCase().replace(/\s+/g, "")];
            for (const o of t) {
                var r = this._translationCache.get(o);
                if (r) return r;
            }
            return e;
        },
        getLocalizedName(e) {
            if (!e) return null;
            this.detectPageLanguage();
            0 === this._reverseLookupCache.size && this.initializeCaches();
            var t = [e, e.toLowerCase(), e.replace(/\s+/g, ""), e.toLowerCase().replace(/\s+/g, "")];
            for (const o of t) {
                var r = this._reverseLookupCache.get(o);
                if (r) return r;
            }
            return e;
        },
    };
})();
