/* CSS Design Tokens and Variables
 * Centralized design system variables for consistent theming
 */

:root {
  /* ===== CORE PALETTE - Raw Colors Only ===== */
  /* These are the base colors that should rarely change */
  --palette-white: #FFFFFF;
  --palette-black: #000000;
  --palette-blue-primary: #470CED;
  --palette-blue-dark: #2A00A0;
  --palette-blue-light: #7B8CFF;
  --palette-blue-accent: #47A3FF;
  --palette-teal: #4ECBC4;
  --palette-orange: #f77f16;
  --palette-purple: #ab05f9;
  --palette-cyan: #11e6ec;
  --palette-green: #04AE2C;
  --palette-red: #FF391F;
  --palette-yellow: #FDC300;
  --palette-gray-50: #F7F8FA;
  --palette-gray-100: #F1F3F4;
  --palette-gray-200: #E9EBF2;
  --palette-gray-300: #DCE0E5;
  --palette-gray-400: #B4B9C5;
  --palette-gray-450: #A3B1CC;
  --palette-gray-500: #606F95;
  --palette-gray-600: #384459;
  --palette-gray-700: #2F3341;
  --palette-gray-750: #2A2F37;
  --palette-gray-800: #1A1D23;
  --palette-gray-900: #111216;
  --palette-gray-950: #1B1D21;

  /* ===== SEMANTIC TOKENS - Meaningful Color Assignments ===== */
  /* These reference palette variables and provide semantic meaning */
  --bg-primary: var(--palette-white);
  --bg-secondary: var(--palette-gray-100);
  --border-color: var(--palette-gray-200);
  --text-primary: var(--palette-gray-500);
  --text-secondary: var(--palette-gray-400);
  --text-accent: var(--palette-gray-950);
  --btn-hover: var(--palette-gray-50);
  --btn-border: var(--palette-gray-300);
  
  /* Action Button Semantic Tokens */
  --action-btn-bg: var(--palette-blue-primary);
  --action-btn-hover: var(--palette-blue-dark);
  --action-btn-disabled: var(--palette-gray-400);
  --action-btn-text: var(--palette-white);
  
  /* Success Color Semantic Token */
  --color-success: var(--palette-green);
  
  /* Additional Color Semantic Tokens */
  --color-danger: var(--palette-red);
  --color-warning: var(--palette-yellow);
  --color-info: var(--palette-blue-primary);
  --color-light-gray: var(--palette-gray-50);
  --color-medium-gray: var(--palette-gray-400);
  --color-dark-gray: var(--palette-gray-600);
  --color-divider: var(--palette-gray-200);
  
  /* Trend and Direction Color Tokens */
  --color-positive: var(--palette-green);
  --color-negative: var(--palette-red);
  --color-increase: var(--palette-green);
  --color-decrease: var(--palette-red);
  --color-up: var(--palette-green);
  --color-down: var(--palette-red);
  --color-rejected: var(--palette-orange);
  
  /* ===== COMPONENT TOKENS - UI-Specific Variables ===== */
  /* These use semantic tokens and provide component-specific styling */
  --loaded-files-border: var(--color-success);
  --loaded-files-text: var(--text-primary);
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: var(--color-success);
  --clear-button-bg: rgba(250, 88, 58, 0.05);
  --clear-button-icon: var(--color-danger);
  --loaded-files-progress-bg: rgba(96, 111, 149, 0.1);
  --loaded-files-progress-fill: var(--color-success);

  /* Add transition properties at root level */
  --theme-transition: transform 0.3s ease,
                     opacity 0.3s ease;

  /* Tooltip variables */
  --tooltip-bg: var(--palette-black);
  --tooltip-text: var(--palette-white);
  --tooltip-font-size: 12px;
  --tooltip-padding: 8px 16px;
  --tooltip-radius: 6px;
  --tooltip-arrow-size: 5px;
  --tooltip-transition: opacity 0.2s ease, visibility 0.2s ease;

  /* Z-index scale for standardized layering - Use these tokens for consistent stacking order
   * Override guidance: Only use higher values when absolutely necessary, prefer semantic tokens */
  --z-base: 0;        /* Base layer - default stacking context */
  --z-surface: 10;    /* Surface elements - cards, panels, elevated content */
  --z-dropdown: 100;  /* Dropdown menus, select options, floating UI */
  --z-tooltip: 200;   /* Tooltips, hints, contextual information */
  --z-modal: 1000;    /* Modal dialogs, overlays, important UI blocking content */
  --z-header: 5000;   /* Dashboard header - above modals, below overlays */
}

[data-theme="dark"] {
  /* ===== DARK THEME PALETTE OVERRIDES ===== */
  /* Only override core palette variables for theme switching */
  --palette-blue-primary: #470CED;
  --palette-blue-dark: #5A6BFF;
  --palette-blue-light: #9BA8FF;
  --palette-blue-accent: #6B9AFF;
  --palette-teal: #4ECBC4;
  --palette-orange: #FF9A4A;
  --palette-purple: #C44DFF;
  --palette-cyan: #4ED8E0;
  
  /* ===== DARK THEME SEMANTIC TOKENS ===== */
  /* These automatically update due to palette variable changes */
  --bg-primary: var(--palette-gray-800);
  --bg-secondary: var(--palette-gray-900);
  --border-color: var(--palette-gray-700);
  --text-primary: var(--palette-gray-400);
  --text-accent: var(--palette-white);
  --btn-hover: var(--palette-gray-700);
  --btn-border: var(--palette-gray-600);
  
  /* Action Button Dark Theme - Explicit variants for accessibility */
  --action-btn-bg: var(--palette-blue-primary);
  --action-btn-hover: var(--palette-blue-dark);
  --action-btn-disabled: var(--palette-gray-700);
  --action-btn-text: var(--palette-white);
  
  /* Additional Color Semantic Tokens for Dark Theme */
  --color-light-gray: var(--palette-gray-900);
  --color-medium-gray: var(--palette-gray-400);
  --color-dark-gray: var(--palette-gray-300);
  --color-divider: var(--palette-gray-700);

  /* Loaded Files UI Dark Theme */
  --loaded-files-border: var(--color-success);
  --loaded-files-text: var(--text-accent);
  --loaded-files-counter-bg: rgba(4, 174, 44, 0.1);
  --loaded-files-counter-text: var(--color-success);
  --clear-button-bg: rgba(250, 88, 58, 0.1);
  --clear-button-icon: var(--color-danger);
  --loaded-files-progress-bg: rgba(255, 255, 255, 0.1);
  --loaded-files-progress-fill: var(--color-success);

  /* Tooltip Dark Theme */
  --tooltip-bg: var(--palette-black);
  --tooltip-text: var(--palette-white);
}
