<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Demo - Advanced Data Grid</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="../../css/tokens.css">
    <link rel="stylesheet" href="../../css/base.css">
    
    <!-- SnapGrid CSS -->
    <link rel="stylesheet" href="snap-grid.css">
    
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            margin-bottom: 30px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-accent);
            margin-bottom: 10px;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
        
        .demo-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .demo-control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-button {
            padding: 8px 16px;
            border: 1px solid var(--btn-border);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .demo-button:hover {
            background: var(--btn-hover);
        }
        
        .demo-button.active {
            background: var(--palette-blue-primary);
            color: white;
            border-color: var(--palette-blue-primary);
        }
        
        .demo-select {
            padding: 6px 12px;
            border: 1px solid var(--btn-border);
            border-radius: 4px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .demo-input {
            padding: 6px 12px;
            border: 1px solid var(--btn-border);
            border-radius: 4px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            width: 200px;
        }
        
        .grid-container {
            height: 600px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .demo-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: var(--bg-secondary);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-accent);
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-accent);
            margin-bottom: 15px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }
        
        .feature-icon {
            color: var(--palette-blue-primary);
            font-weight: bold;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px;
            border: 1px solid var(--btn-border);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .demo-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .demo-control-group {
                justify-content: space-between;
            }
            
            .grid-container {
                height: 400px;
            }
            
            .demo-stats {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Theme</button>
    
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">SnapGrid Demo</h1>
            <p class="demo-subtitle">Advanced Data Grid with AG Grid-level functionality and modern design</p>
        </div>
        
        <div class="demo-section">
            <h2 class="section-title">✨ Key Features</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">⚡</span>
                    <span>Virtual Scrolling for Performance</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔍</span>
                    <span>Advanced Filtering & Sorting</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✏️</span>
                    <span>Inline Cell Editing</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span>Multiple Themes & Dark Mode</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span>Responsive Design</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">♿</span>
                    <span>Full Accessibility Support</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <span>Data Export (CSV)</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⌨️</span>
                    <span>Keyboard Navigation</span>
                </div>
            </div>
        </div>
        
        <div class="demo-controls">
            <div class="demo-control-group">
                <label>Theme:</label>
                <select class="demo-select" id="themeSelect">
                    <option value="default">Default</option>
                    <option value="compact">Compact</option>
                    <option value="dense">Dense</option>
                    <option value="comfortable">Comfortable</option>
                </select>
            </div>
            
            <div class="demo-control-group">
                <label>Rows:</label>
                <select class="demo-select" id="rowCountSelect">
                    <option value="100">100 rows</option>
                    <option value="500">500 rows</option>
                    <option value="1000" selected>1,000 rows</option>
                    <option value="5000">5,000 rows</option>
                    <option value="10000">10,000 rows</option>
                </select>
            </div>
            
            <div class="demo-control-group">
                <input type="text" class="demo-input" id="filterInput" placeholder="Filter by name...">
                <button class="demo-button" onclick="applyFilter()">Filter</button>
                <button class="demo-button" onclick="clearFilter()">Clear</button>
            </div>
            
            <div class="demo-control-group">
                <button class="demo-button" onclick="exportData()">📊 Export CSV</button>
                <button class="demo-button" onclick="refreshGrid()">🔄 Refresh</button>
                <button class="demo-button" onclick="clearSelection()">❌ Clear Selection</button>
            </div>
        </div>
        
        <div class="demo-stats">
            <div class="stat-card">
                <div class="stat-label">Total Rows</div>
                <div class="stat-value" id="totalRows">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Filtered Rows</div>
                <div class="stat-value" id="filteredRows">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Selected Rows</div>
                <div class="stat-value" id="selectedRows">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Render Time</div>
                <div class="stat-value" id="renderTime">0ms</div>
            </div>
        </div>
        
        <div class="grid-container">
            <div id="demoGrid"></div>
        </div>
        
        <div class="demo-section">
            <h2 class="section-title">🚀 Performance</h2>
            <p>This grid efficiently handles large datasets using virtual scrolling. Try changing the row count to see how it performs with thousands of rows while maintaining smooth scrolling and interaction.</p>
        </div>
    </div>
    
    <!-- SnapGrid JavaScript -->
    <script src="snap-grid.js"></script>
    <script src="snap-grid-demo.js"></script>
</body>
</html>
