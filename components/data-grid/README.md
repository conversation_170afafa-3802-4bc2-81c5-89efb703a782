# SnapGrid - Advanced Data Grid Component

A comprehensive, high-performance data grid component inspired by AG Grid, built specifically for the Snap Dashboard ecosystem with modern UI design and full accessibility support.

## 🚀 Features

### Core Functionality
- **Virtual Scrolling** - Efficiently handle thousands of rows with smooth performance
- **Column Management** - Sorting, filtering, resizing, and reordering
- **Cell Editing** - Inline editing with validation and callbacks
- **Row Selection** - Single and multi-row selection support
- **Data Export** - Export to CSV with customizable formatting
- **Keyboard Navigation** - Full keyboard accessibility with arrow keys

### Advanced Features
- **Column Menus** - Context menus for sorting and filtering
- **Custom Cell Renderers** - Flexible cell content customization
- **Multiple Themes** - Default, compact, dense, and comfortable themes
- **Dark Mode Support** - Seamless integration with design system themes
- **Responsive Design** - Mobile-friendly with touch support
- **Performance Monitoring** - Built-in render time tracking

### Accessibility
- **ARIA Support** - Full screen reader compatibility
- **Keyboard Navigation** - Complete keyboard-only operation
- **High Contrast Mode** - Support for accessibility preferences
- **Screen Reader Announcements** - Live updates for data changes

## 📦 Installation

### Basic Setup
```html
<!-- Include CSS -->
<link rel="stylesheet" href="components/data-grid/snap-grid.css">

<!-- Include JavaScript -->
<script src="components/data-grid/snap-grid.js"></script>
```

### With Design System
```html
<!-- Design System CSS (required) -->
<link rel="stylesheet" href="css/tokens.css">
<link rel="stylesheet" href="css/base.css">

<!-- SnapGrid CSS -->
<link rel="stylesheet" href="components/data-grid/snap-grid.css">
```

## 🎯 Quick Start

### Basic Grid
```javascript
// Create container
const container = document.getElementById('myGrid');

// Define columns
const columns = [
    { field: 'name', headerName: 'Product Name', width: 200 },
    { field: 'price', headerName: 'Price', type: 'currency', width: 120 },
    { field: 'stock', headerName: 'Stock', type: 'number', width: 100 }
];

// Sample data
const data = [
    { name: 'iPhone 14', price: 999, stock: 150 },
    { name: 'Samsung S23', price: 899, stock: 200 },
    { name: 'MacBook Pro', price: 1999, stock: 75 }
];

// Create grid
const grid = new SnapGrid(container, {
    data: data,
    columns: columns,
    sortable: true,
    filterable: true,
    editable: true
});
```

### Advanced Configuration
```javascript
const grid = new SnapGrid(container, {
    data: data,
    columns: columns,
    
    // Performance options
    virtualScrolling: true,
    rowHeight: 40,
    bufferSize: 10,
    
    // Feature options
    sortable: true,
    filterable: true,
    resizable: true,
    selectable: true,
    editable: true,
    
    // UI options
    theme: 'compact',
    showHeader: true,
    
    // Callbacks
    onRowClick: (rowData, rowIndex, event) => {
        console.log('Row clicked:', rowData);
    },
    
    onCellEdit: (field, newValue, oldValue, rowData, rowIndex) => {
        console.log('Cell edited:', { field, newValue, oldValue });
    },
    
    onSort: (field, direction) => {
        console.log('Sort applied:', { field, direction });
    },
    
    onFilter: (field, value, type) => {
        console.log('Filter applied:', { field, value, type });
    }
});
```

## 📋 Column Configuration

### Column Properties
```javascript
const column = {
    field: 'productName',           // Data field name (required)
    headerName: 'Product Name',     // Display name
    width: 200,                     // Column width in pixels
    type: 'text',                   // Data type: text, number, currency, date, boolean
    sortable: true,                 // Enable sorting
    filterable: true,               // Enable filtering
    editable: true,                 // Enable editing
    resizable: true,                // Enable resizing
    cellRenderer: (value, column, rowData, rowIndex) => {
        // Custom cell renderer function
        return `<strong>${value}</strong>`;
    }
};
```

### Data Types
- **text** - Default text rendering
- **number** - Formatted numbers with locale support
- **currency** - Currency formatting ($1,234.56)
- **date** - Date formatting with locale support
- **boolean** - Checkmark/X display

### Custom Cell Renderers
```javascript
{
    field: 'status',
    headerName: 'Status',
    cellRenderer: (value) => {
        const color = value === 'active' ? '#4CAF50' : '#F44336';
        return `<span style="color: ${color}">●</span> ${value}`;
    }
}
```

## 🎨 Themes

### Available Themes
- **default** - Standard spacing and sizing
- **compact** - Reduced padding and row height
- **dense** - Minimal spacing for maximum data density
- **comfortable** - Increased spacing for better readability

### Theme Usage
```javascript
// Set theme during initialization
const grid = new SnapGrid(container, {
    theme: 'compact',
    // ... other options
});

// Change theme dynamically
container.className = 'snap-grid dense';
```

## 🔧 API Reference

### Methods

#### Data Management
```javascript
// Update grid data
grid.updateData(newData);

// Get all data
const allData = grid.getData();

// Get filtered/sorted data
const displayedData = grid.getDisplayedData();

// Get selected rows
const selectedData = grid.getSelectedData();
```

#### Selection
```javascript
// Clear all selections
grid.clearSelection();
```

#### Filtering
```javascript
// Set filter
grid.setFilter('name', 'iPhone', 'contains');

// Clear all filters
grid.clearFilters();
```

#### Sorting
```javascript
// Set sort
grid.setSort('price', 'desc');

// Clear all sorting
grid.clearSort();
```

#### Utility
```javascript
// Refresh grid
grid.refresh();

// Resize grid (call after container size changes)
grid.resize();

// Export to CSV
grid.exportToCsv('my-data.csv');

// Get performance statistics
const stats = grid.getStats();

// Destroy grid
grid.destroy();
```

### Events

#### Row Events
```javascript
onRowClick: (rowData, rowIndex, event) => {
    // Handle row click
}
```

#### Cell Events
```javascript
onCellEdit: (field, newValue, oldValue, rowData, rowIndex) => {
    // Handle cell edit
}
```

#### Data Events
```javascript
onSort: (field, direction) => {
    // Handle sort change
},

onFilter: (field, value, type) => {
    // Handle filter change
}
```

## 🎮 Demo

Open `snap-grid-demo.html` in your browser to see all features in action:

```bash
# Serve the demo locally
python -m http.server 8000
# Then visit: http://localhost:8000/components/data-grid/snap-grid-demo.html
```

## 🔗 Integration

### Snap Dashboard Integration
```javascript
// Use the integration component
import snapGridIntegrationComponent from './snap-grid-integration.js';

// Initialize in dashboard
snapGridIntegrationComponent.render();
```

### Custom Integration
```javascript
// Create grid with dashboard data
const grid = new SnapGrid(container, {
    data: dashboardData,
    columns: dashboardColumns,
    onCellEdit: (field, newValue, oldValue, rowData) => {
        // Sync changes to backend
        updateProductData(rowData.id, field, newValue);
    }
});
```

## 🚀 Performance

### Optimization Features
- **Virtual Scrolling** - Only renders visible rows
- **DOM Recycling** - Reuses DOM elements during scrolling
- **Efficient Updates** - Minimal DOM manipulation
- **Memory Management** - Automatic cleanup and optimization

### Performance Tips
- Use virtual scrolling for datasets > 100 rows
- Set appropriate `bufferSize` for your use case
- Implement efficient `cellRenderer` functions
- Use `refresh()` instead of recreating the grid

## ♿ Accessibility

### ARIA Support
- Grid role structure with proper row/cell roles
- Column headers with sort state announcements
- Screen reader announcements for data changes
- Keyboard navigation support

### Keyboard Navigation
- **Arrow Keys** - Navigate between cells
- **Enter** - Start editing cell
- **Escape** - Cancel editing
- **Tab** - Move to next focusable element

## 🎯 Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📄 License

Part of the Snap Dashboard project. See main project license for details.
